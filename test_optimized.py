"""
Test the optimized coaching agent on a small sample
"""
from optimized_coaching_agent import OptimizedCoachingAgent
from file_utils import get_recent_files
import config

def test_smart_scanning():
    """Test the smart conversation scanning on a few files."""
    print("🧪 Testing Optimized Coaching Agent")
    print("=" * 50)
    
    # Get a small sample of files
    recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, 30)
    
    if not recent_files:
        print("❌ No recent files found")
        return
    
    # Test with first 3 files
    test_files = recent_files[:3]
    print(f"🔬 Testing with {len(test_files)} files:")
    for file_path in test_files:
        print(f"  • {file_path.split('/')[-1]}")
    
    # Create optimized agent
    agent = OptimizedCoachingAgent(batch_size=3, max_workers=2)
    
    # Test single file processing
    print(f"\n📄 Testing single file processing...")
    result = agent.process_single_chat_optimized(test_files[0])
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ Success! Quality: {result.coaching_quality}/10")
        print(f"   Arc type: {result.arc_type}")
        print(f"   Themes: {', '.join(result.key_themes[:3])}")
        
        # Show optimization stats
        if result.metadata and 'optimization_stats' in result.metadata:
            stats = result.metadata['optimization_stats']
            print(f"   Token savings: {stats['token_savings_percent']}%")
            print(f"   Key moments: {stats['key_moments_extracted']}")
    
    # Test batch processing
    print(f"\n🔄 Testing batch processing...")
    batch_results = agent.process_batch_concurrent(test_files)
    
    successful = sum(1 for r in batch_results.values() if hasattr(r, 'coaching_quality'))
    print(f"✅ Batch complete: {successful}/{len(test_files)} successful")
    
    # Show token savings summary
    total_original = 0
    total_optimized = 0
    
    for result in batch_results.values():
        if hasattr(result, 'metadata') and result.metadata and 'optimization_stats' in result.metadata:
            stats = result.metadata['optimization_stats']
            total_original += stats['original_tokens']
            total_optimized += stats['optimized_tokens']
    
    if total_original > 0:
        savings = (total_original - total_optimized) / total_original * 100
        print(f"💰 Total token savings: {total_original:,} → {total_optimized:,} ({savings:.1f}% reduction)")
    
    print(f"\n🎯 Test complete! Ready for full processing.")

if __name__ == "__main__":
    test_smart_scanning()
