"""
File utilities for the Coaching Recap Agent
"""
import os
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import config

def get_recent_files(directory: str, days_back: int = config.DAYS_LOOKBACK) -> List[str]:
    """
    Get all JSON files modified within the specified number of days.
    
    Args:
        directory: Directory to scan for files
        days_back: Number of days to look back
        
    Returns:
        List of file paths that were modified within the timeframe
    """
    cutoff_date = datetime.now() - timedelta(days=days_back)
    recent_files = []
    
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory {directory} does not exist")
        return recent_files
    
    for file_path in directory_path.glob("*.json"):
        try:
            # Get file modification time
            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            
            if mod_time >= cutoff_date:
                recent_files.append(str(file_path))
                print(f"Found recent file: {file_path.name} (modified: {mod_time.strftime('%Y-%m-%d %H:%M')})")
        except Exception as e:
            print(f"Error checking file {file_path}: {e}")
    
    return sorted(recent_files)

def load_json_file(file_path: str) -> Dict[Any, Any]:
    """
    Load and parse a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        Parsed JSON data as dictionary
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON file {file_path}: {e}")
        return {}

def save_json_file(data: Dict[Any, Any], file_path: str) -> bool:
    """
    Save data to a JSON file.
    
    Args:
        data: Data to save
        file_path: Path where to save the file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving JSON file {file_path}: {e}")
        return False

def ensure_output_directory():
    """Ensure the output directory exists."""
    Path(config.OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
