{"id": "683c683f-99bc-800e-acd0-80ad8a80755e", "title": "Story Frameworks and Methods", "created": 1748789311965, "updated": 1748789358993, "currentMessage": "4dc72b5b-d5c7-4efb-a5c7-b65bd8147283", "messages": [{"id": "bbb2100d-58b9-4fc2-bc84-f8694ce21da3", "parent": null, "role": "user", "content": "What framework are you based on", "timestamp": 1748789311973.79}, {"id": "39b31147-c5de-45d9-8721-8e44a41d99f7", "parent": "bbb2100d-58b9-4fc2-bc84-f8694ce21da3", "role": "tool", "content": "\nPlease remember to search the user's documents if an answer to their question is not contained in the above snippets. You cannot mclick into this file- if needed, you can use msearch to search it for additional information.\n", "timestamp": 1748789320774.2551}, {"id": "321a056d-8be4-4b72-a4bb-f230b69f7c32", "parent": "39b31147-c5de-45d9-8721-8e44a41d99f7", "role": "assistant", "content": "The frameworks I draw from in analyzing and helping with story creation are based on a synthesis of multiple structured methodologies found in the following resources you've provided:\n\n1. **\"Story Structure for the Win\" by <PERSON><PERSON><PERSON><PERSON>’Costa**: This framework emphasizes six key structural turning points — inciting incident, first-act break, midpoint, trough of hell, climax, and resolution — aimed at helping both plotters and pantsers write faster and with greater direction【9†source】.\n\n2. **\"Writing a Successful TV Series\" by <PERSON>**: This book introduces the *Story-Type Method*, which distinguishes between story structure and story format. It focuses on dramatic (not logistical) structure, information management (mystery, suspense, irony), and matching story types (plot-led, character-led, theme-led) with the right series formats【10†source】.\n\n3. **\"Beat by Beat\" by <PERSON>**: Offers a granular, minute-by-minute beat structure of successful screenplays. It's a tool to track pacing and key emotional or plot beats in a linear structure【11†source】.\n\n4. **\"7 Figure Fiction\" by <PERSON><PERSON>**: Focuses on *Universal Fantasy* — core emotional fantasies shared across readers — to enhance reader engagement and marketability. It’s less about structure and more about thematic resonance and emotional hooks【12†source】.\n\n5. **\"Binge Worthy Framework\" from Breaking Bad case study**: A character-driven model highlighting emotional depth, relationship hooks, mystery, and moral dilemmas across episodes, designed for serialized storytelling. It combines structural beats with psychological profiling of characters and open loops for ongoing engagement【13†source】.\n\nI use these frameworks to offer practical, customizable story development advice and analysis, ensuring stories are both structurally sound and emotionally compelling. If you have a specific story or format in mind, I can tailor the approach accordingly.", "timestamp": 1748789313100.568}, {"id": "bbb219b1-e74a-4c38-833d-b4d0a78fe085", "parent": "321a056d-8be4-4b72-a4bb-f230b69f7c32", "role": "user", "content": "He had to break this into eight if you had to break this into eight steps what would they be sort of the main pillars so you know either you're starting with one of four it's like you got a character you got a theme or whatever organizer like that", "timestamp": 1748789346330.924}, {"id": "4dc72b5b-d5c7-4efb-a5c7-b65bd8147283", "parent": "bbb219b1-e74a-4c38-833d-b4d0a78fe085", "role": "assistant", "content": "To create a unified, flexible 8-step framework synthesizing the core methods from the documents, here's a streamlined set of \"main pillars\" for building a compelling, binge-worthy story — whether for novels or TV series:\n\n---\n\n### **UF 8-Pillar Story Framework**\n\n1. **Start with the Core Spark**\n   - Pick **one of four story foundations**:\n     - **Character**: A deeply flawed but compelling protagonist.\n     - **Theme**: A powerful universal idea or moral (justice, survival, freedom).\n     - **Plot**: A high-stakes external conflict or dramatic situation.\n     - **Fantasy**: A *Universal Fantasy* that deeply resonates (e.g., redemption, secret royalty, revenge).\n\n2. **Define the Concept & Conflict**\n   - Distill into a *high-concept hook*: A clear situation + stakes + twist.\n   - Set up the **core external conflict** that drives the plot (e.g., Breaking Bad’s “teacher turns meth kingpin”).\n\n3. **Design the Character Engine**\n   - Determine:\n     - **Want** (external goal) vs. **Need** (emotional flaw or lesson).\n     - Emotional depth (fear/hope/trauma).\n     - Secret identity or wound.\n     - Relationship and identity masks (public vs. private).\n\n4. **Set the Story World & Stakes**\n   - Introduce a **rich, intriguing world** (internal and external).\n   - Build layers of tension: moral dilemmas, power structures, consequences.\n\n5. **Structure the Journey (3-Act Backbone)**\n   - **Act 1**: Set up & inciting incident.\n   - **Act 2A**: Rising complications & midpoint twist.\n   - **Act 2B**: All is lost / trough of hell.\n   - **Act 3**: Climax and resolution.\n   - *Use dramatic structure, not just page-counts or beats.*\n\n6. **Build Bingeable Beats & Hooks**\n   - Layer in **episode/story beat “moments”**: cliffhangers, reveals, ironic reversals.\n   - Leverage surprise, mystery, suspense, and dramatic irony.\n\n7. **Interweave Emotional & Relational Arcs**\n   - Develop **key relationship hooks** (conflicted alliances, romantic tension, betrayal).\n   - Show **internal transformations** over time.\n\n8. **Close Loops, Payoff the Fantasy**\n   - Deliver emotionally satisfying **climax** and thematic resolution.\n   - Resolve or subvert **the Universal Fantasy** introduced.\n   - Leave **open loops** (if serialized) for future engagement.\n\n---\n\nThis framework bridges plot, character, structure, and emotional connection while allowing for flexibility depending on what spark you begin with. Want help applying this to a story idea or outline?", "timestamp": 1748789346445.395}]}