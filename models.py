"""
Pydantic data models for the Coaching Recap Agent
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, validator
import config

class ChatMessage(BaseModel):
    """Model for individual chat messages."""
    role: Literal["user", "assistant", "system", "tool"] = Field(..., description="Role of the message sender")
    content: str = Field(..., description="Content of the message")
    timestamp: Optional[float] = Field(None, description="Unix timestamp of the message")
    
    @validator('content')
    def content_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Content cannot be empty')
        return v.strip()

class ChatMetadata(BaseModel):
    """Model for chat file metadata."""
    title: str = Field(default="Untitled", description="Title of the conversation")
    created: Optional[float] = Field(None, description="Unix timestamp when chat was created")
    updated: Optional[float] = Field(None, description="Unix timestamp when chat was last updated")
    message_count: int = Field(default=0, description="Number of messages in the chat")
    id: Optional[str] = Field(None, description="Unique identifier for the chat")
    file_path: Optional[str] = Field(None, description="Path to the source file")
    file_name: Optional[str] = Field(None, description="Name of the source file")

class TurningPoint(BaseModel):
    """Model for a turning point in the conversation."""
    moment: str = Field(..., description="Brief description of what happened")
    insight: str = Field(..., description="What realization or shift occurred")
    significance: str = Field(..., description="Why this was important to the overall arc")

class CoachingAnalysis(BaseModel):
    """Model for the complete coaching conversation analysis."""
    start_point: str = Field(..., description="Initial concern, question, or goal")
    turning_points: List[TurningPoint] = Field(default_factory=list, description="Key moments of shift or breakthrough")
    resolution: str = Field(..., description="Final insight, decision, or resolution reached")
    open_loops: List[str] = Field(default_factory=list, description="Unresolved questions or threads")
    arc_type: Literal[
        "emotional_breakthrough",
        "creative_clarity", 
        "decision_making_loop",
        "structural_tension",
        "identity_integration",
        "resistance_breakdown",
        "visionary_expansion",
        "collaboration_wound",
        "sovereignty_reclaim",
        "other"
    ] = Field(..., description="Type of coaching arc")
    key_themes: List[str] = Field(default_factory=list, description="Major themes in the conversation")
    coaching_quality: int = Field(..., ge=1, le=10, description="Coaching effectiveness rating 1-10")
    integration_potential: str = Field(..., description="Most valuable takeaway or action")
    
    # Metadata fields
    metadata: Optional[Dict[str, Any]] = Field(None, description="Analysis metadata")
    file_metadata: Optional[ChatMetadata] = Field(None, description="Source file metadata")

class ThemeFrequency(BaseModel):
    """Model for theme frequency data."""
    theme: str = Field(..., description="Theme name")
    frequency: int = Field(..., ge=0, description="Number of occurrences")

class HighQualitySession(BaseModel):
    """Model for high-quality session data."""
    title: str = Field(..., description="Session title")
    quality: int = Field(..., ge=1, le=10, description="Quality rating")
    arc_type: str = Field(..., description="Type of coaching arc")
    file_name: Optional[str] = Field(None, description="Source file name")

class IntegrationOpportunity(BaseModel):
    """Model for integration opportunity data."""
    title: str = Field(..., description="Session title")
    opportunity: str = Field(..., description="Integration opportunity description")
    file_name: Optional[str] = Field(None, description="Source file name")

class ProcessingStats(BaseModel):
    """Model for processing statistics."""
    successful: int = Field(..., ge=0, description="Number of successful analyses")
    failed: int = Field(..., ge=0, description="Number of failed analyses")
    success_rate: str = Field(..., description="Success rate as percentage string")

class AnalysisSummary(BaseModel):
    """Model for the analysis summary."""
    period: str = Field(..., description="Time period analyzed")
    total_conversations: int = Field(..., ge=0, description="Total conversations analyzed")
    arc_type_distribution: Dict[str, int] = Field(default_factory=dict, description="Distribution of arc types")
    top_themes: List[ThemeFrequency] = Field(default_factory=list, description="Most frequent themes")
    high_quality_sessions: List[HighQualitySession] = Field(default_factory=list, description="Sessions with high quality ratings")
    integration_opportunities: List[IntegrationOpportunity] = Field(default_factory=list, description="Key integration opportunities")
    processing_stats: ProcessingStats = Field(..., description="Processing statistics")

class RecapMetadata(BaseModel):
    """Model for recap processing metadata."""
    processed_at: str = Field(..., description="ISO timestamp when processing completed")
    days_lookback: int = Field(..., ge=1, description="Number of days looked back")
    total_files_found: int = Field(..., ge=0, description="Total files found in timeframe")
    successful_analyses: int = Field(..., ge=0, description="Number of successful analyses")
    failed_analyses: int = Field(..., ge=0, description="Number of failed analyses")
    model_used: Optional[str] = Field(None, description="AI model used for analysis")

class CoachingRecapResult(BaseModel):
    """Model for the complete coaching recap result."""
    summary: AnalysisSummary = Field(..., description="Summary of all analyses")
    individual_analyses: Dict[str, CoachingAnalysis] = Field(default_factory=dict, description="Individual conversation analyses")
    metadata: RecapMetadata = Field(..., description="Processing metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ChatExportData(BaseModel):
    """Model for raw chat export data structure."""
    id: Optional[str] = Field(None, description="Chat ID")
    title: Optional[str] = Field(None, description="Chat title")
    created: Optional[float] = Field(None, description="Creation timestamp")
    updated: Optional[float] = Field(None, description="Update timestamp")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="Raw message data")
    
    def to_metadata(self) -> ChatMetadata:
        """Convert to ChatMetadata model."""
        return ChatMetadata(
            title=self.title or "Untitled",
            created=self.created,
            updated=self.updated,
            message_count=len(self.messages),
            id=self.id
        )
    
    def extract_messages(self) -> List[ChatMessage]:
        """Extract and validate messages."""
        messages = []
        
        # Sort by timestamp if available
        raw_messages = self.messages
        if raw_messages and isinstance(raw_messages[0], dict) and 'timestamp' in raw_messages[0]:
            raw_messages = sorted(raw_messages, key=lambda x: x.get('timestamp', 0))
        
        for msg_data in raw_messages:
            if not isinstance(msg_data, dict):
                continue
                
            role = msg_data.get('role', 'unknown')
            content = msg_data.get('content', '')
            timestamp = msg_data.get('timestamp')
            
            # Skip empty messages or non-conversational roles
            if not content.strip() or role not in ['user', 'assistant', 'system']:
                continue
            
            try:
                message = ChatMessage(
                    role=role,
                    content=content,
                    timestamp=timestamp
                )
                messages.append(message)
            except Exception as e:
                print(f"Warning: Skipping invalid message: {e}")
                continue
        
        return messages

# Validation functions
def validate_arc_type(arc_type: str) -> bool:
    """Validate that arc_type is one of the allowed values."""
    return arc_type in config.ARC_TYPES

def validate_quality_rating(rating: int) -> bool:
    """Validate that quality rating is between 1 and 10."""
    return 1 <= rating <= 10
