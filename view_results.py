"""
View and format the coaching recap results
"""
import json
from pathlib import Path
from datetime import datetime

def load_results():
    """Load the results from the output file."""
    results_file = Path("output/30_day_recap.json")
    
    if not results_file.exists():
        print("❌ Results file not found. Make sure the analysis has completed.")
        return None
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading results: {e}")
        return None

def format_summary(summary):
    """Format the summary section."""
    print("📊 COACHING RECAP SUMMARY")
    print("=" * 50)
    
    print(f"Period: {summary.get('period', 'Unknown')}")
    print(f"Total Conversations: {summary.get('total_conversations', 0)}")
    
    # Processing stats
    stats = summary.get('processing_stats', {})
    print(f"Success Rate: {stats.get('success_rate', 'Unknown')}")
    print(f"Successful: {stats.get('successful', 0)}")
    print(f"Failed: {stats.get('failed', 0)}")
    
    # Arc type distribution
    arc_dist = summary.get('arc_type_distribution', {})
    if arc_dist:
        print(f"\n🎭 ARC TYPE DISTRIBUTION:")
        for arc_type, count in sorted(arc_dist.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {arc_type.replace('_', ' ').title()}: {count}")
    
    # Top themes
    themes = summary.get('top_themes', [])
    if themes:
        print(f"\n🎯 TOP THEMES:")
        for theme in themes[:10]:
            if isinstance(theme, dict):
                print(f"  • {theme.get('theme', 'Unknown')} ({theme.get('frequency', 0)}x)")
            else:
                print(f"  • {theme}")
    
    # High quality sessions
    high_quality = summary.get('high_quality_sessions', [])
    if high_quality:
        print(f"\n⭐ HIGH QUALITY SESSIONS (8+ rating):")
        for session in high_quality[:5]:
            if isinstance(session, dict):
                title = session.get('title', 'Unknown')
                quality = session.get('quality', 0)
                arc_type = session.get('arc_type', 'unknown')
                print(f"  • {title} (Quality: {quality}/10, Type: {arc_type})")
    
    # Integration opportunities
    integration = summary.get('integration_opportunities', [])
    if integration:
        print(f"\n💡 KEY INTEGRATION OPPORTUNITIES:")
        for opp in integration[:5]:
            if isinstance(opp, dict):
                title = opp.get('title', 'Unknown')
                opportunity = opp.get('opportunity', 'No description')
                print(f"  • {title}")
                print(f"    → {opportunity}")

def show_individual_highlights(individual_analyses):
    """Show highlights from individual analyses."""
    print(f"\n📄 INDIVIDUAL CONVERSATION HIGHLIGHTS")
    print("=" * 50)
    
    # Find highest quality conversations
    quality_conversations = []
    
    for file_path, analysis in individual_analyses.items():
        if isinstance(analysis, dict) and 'coaching_quality' in analysis:
            quality_conversations.append({
                'file': Path(file_path).name,
                'quality': analysis.get('coaching_quality', 0),
                'arc_type': analysis.get('arc_type', 'unknown'),
                'start_point': analysis.get('start_point', 'No start point'),
                'resolution': analysis.get('resolution', 'No resolution'),
                'integration_potential': analysis.get('integration_potential', 'No integration noted')
            })
    
    # Sort by quality
    quality_conversations.sort(key=lambda x: x['quality'], reverse=True)
    
    print(f"🏆 TOP 5 HIGHEST QUALITY CONVERSATIONS:")
    for i, conv in enumerate(quality_conversations[:5], 1):
        print(f"\n{i}. {conv['file']} (Quality: {conv['quality']}/10)")
        print(f"   Arc Type: {conv['arc_type'].replace('_', ' ').title()}")
        print(f"   Start: {conv['start_point'][:100]}...")
        print(f"   Resolution: {conv['resolution'][:100]}...")
        print(f"   Integration: {conv['integration_potential'][:100]}...")

def main():
    """Main function to display results."""
    print("🤖 Coaching Recap Results Viewer")
    print("=" * 60)
    
    results = load_results()
    if not results:
        return
    
    # Show metadata
    metadata = results.get('metadata', {})
    print(f"📅 Processed: {metadata.get('processed_at', 'Unknown time')}")
    print(f"🔍 Lookback: {metadata.get('days_lookback', 'Unknown')} days")
    print(f"📁 Files Found: {metadata.get('total_files_found', 0)}")
    print()
    
    # Show summary
    summary = results.get('summary', {})
    if summary:
        format_summary(summary)
    
    # Show individual highlights
    individual = results.get('individual_analyses', {})
    if individual:
        show_individual_highlights(individual)
    
    print(f"\n✅ Complete results available in: output/30_day_recap.json")
    print(f"📊 Total data size: {Path('output/30_day_recap.json').stat().st_size / 1024:.1f} KB")

if __name__ == "__main__":
    main()
