{"id": "673194e5-5110-4aab-ac6a-65260f7a50c5", "title": "Disabling Email Collection in Notion Forms", "created": 1747925696137, "updated": 1747928300338, "currentMessage": "32b4e451-b9f1-4adf-b315-8ba91a060167", "messages": [{"id": "b0bb136c-2bc4-4018-b5a7-b4fb077bc6ba", "parent": "00000000-0000-4000-8000-000000000000", "role": "user", "content": "i have a notion form using for jobs i don't want at the end to ask for email to see results how do i turn that off", "timestamp": 1747925702396}, {"id": "9c14f4ac-bd2b-4885-9240-22841fa0d982", "parent": "b0bb136c-2bc4-4018-b5a7-b4fb077bc6ba", "role": "assistant", "content": " I can help you disable the email collection at the end of your Notion form! This is a common setting that can be easily turned off.\n\nBefore I provide the exact steps, could you clarify a couple things:\n\n1. Are you using Notion's built-in form feature, or are you using a third-party form tool that integrates with Notion?\n2. When you say \"jobs I don't want\" - are you creating a form to track job applications or rejections?\n\nIn general, for Notion forms, you should be able to:\n- Go to your form settings\n- Look for an option like \"Collect email addresses\" or \"Require email for results\"\n- Toggle that setting off\n\nBut the exact location depends on which type of form setup you're using. Once you let me know more details about your setup, I can give you the specific steps to disable that email requirement!", "timestamp": 1747925737443}, {"id": "a3104d64-93e1-4488-8601-b92e3a27e4e4", "parent": "9c14f4ac-bd2b-4885-9240-22841fa0d982", "role": "user", "content": "built in form", "timestamp": 1747925820696}, {"id": "9b9c8ec4-fc39-4c12-bb4a-798f9a2c6cc9", "parent": "9c14f4ac-bd2b-4885-9240-22841fa0d982", "role": "user", "content": "built in form", "timestamp": 1747925837278}, {"id": "ce2f5008-ee57-41cf-9e70-0f3176a78c22", "parent": "9b9c8ec4-fc39-4c12-bb4a-798f9a2c6cc9", "role": "assistant", "content": " Perfect! For Notion's built-in form feature, here's how to turn off the email collection:\n\n1. **Open your Notion form** (the one you created from your database)\n\n2. **Click on the form settings** - look for a settings/gear icon at the top of your form, or click \"Settings\" if you see that option\n\n3. **Find the \"Collect email addresses\" or \"Email collection\" setting** - this is usually in the form configuration area\n\n4. **Toggle it OFF** - there should be a switch or checkbox you can disable\n\n5. **Save/Update your form** - the changes should apply automatically, but make sure to save if prompted\n\nIf you're having trouble finding these settings, try:\n- Looking for a \"Share\" or \"Publish\" button on your form, which often contains these privacy settings\n- Checking if there's a \"Form options\" or \"Advanced settings\" section\n\nOnce you turn this off, people will be able to submit your form without providing their email address to see results.\n\nAre you able to locate the form settings, or would you like me to help you find them in a specific area of your Notion workspace?", "timestamp": 1747925871165}, {"id": "487d67f1-0bca-4a8a-afc3-ec391fa2aebb", "parent": "ce2f5008-ee57-41cf-9e70-0f3176a78c22", "role": "user", "content": "i don't see any settings other thatn this", "timestamp": 1747926012238}, {"id": "32b4e451-b9f1-4adf-b315-8ba91a060167", "parent": "487d67f1-0bca-4a8a-afc3-ec391fa2aebb", "role": "assistant", "content": " I can see you're in the Customize panel on the right side! The email collection setting should be in there. Looking at your screenshot, I can see you have sections for:\n\n- Submit button\n- Confirmation title  \n- Confirmation body\n\n**Try scrolling down in that Customize panel** - there should be additional options below what's currently visible, including email collection settings.\n\nIf you don't see it by scrolling, try these steps:\n\n1. **Click on \"Confirmation body\"** - sometimes the email collection setting is grouped under the confirmation settings\n\n2. **Look for any section labeled:**\n   - \"Email collection\"\n   - \"Collect responses\" \n   - \"Response settings\"\n   - \"Submission settings\"\n\n3. **If you still don't see it**, try clicking the **\"Change\"** button next to where it says \"This form is public. Anyone with the link can submit a response\" - this might open additional privacy/collection settings\n\nThe setting you're looking for will likely be a toggle that says something like \"Collect email addresses to view responses\" or \"Require email for results.\"\n\nCan you scroll down in that Customize panel and let me know what other options you see?", "timestamp": 1747926027103}, {"id": "9e1faecb-9b67-41dc-887d-cefe0a62ab1d", "parent": "32b4e451-b9f1-4adf-b315-8ba91a060167", "role": "user", "content": "they are just open boxes with no choices", "timestamp": 1747928300338}]}