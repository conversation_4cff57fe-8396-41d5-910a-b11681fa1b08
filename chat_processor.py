"""
Chat processing utilities for extracting and formatting conversation data
"""
import json
from typing import List, Dict, Any, Optional
import tiktoken
import config

def extract_messages_from_chat(chat_data: Dict[Any, Any]) -> List[Dict[str, Any]]:
    """
    Extract messages from ChatGPT/Claude JSON format in chronological order.
    
    Args:
        chat_data: Parsed JSON data from chat export
        
    Returns:
        List of messages in chronological order
    """
    messages = []
    
    if 'messages' not in chat_data:
        print("No messages found in chat data")
        return messages
    
    # Sort messages by timestamp if available
    raw_messages = chat_data['messages']
    if raw_messages and 'timestamp' in raw_messages[0]:
        raw_messages = sorted(raw_messages, key=lambda x: x.get('timestamp', 0))
    
    for msg in raw_messages:
        # Extract relevant fields
        message = {
            'role': msg.get('role', 'unknown'),
            'content': msg.get('content', ''),
            'timestamp': msg.get('timestamp', 0)
        }
        
        # Skip empty messages or tool messages that don't add value
        if message['content'].strip() and message['role'] in ['user', 'assistant']:
            messages.append(message)
    
    return messages

def format_conversation_for_analysis(messages: List[Dict[str, Any]], title: str = "") -> str:
    """
    Format messages into a readable conversation format for GPT analysis.
    
    Args:
        messages: List of message dictionaries
        title: Optional title for the conversation
        
    Returns:
        Formatted conversation string
    """
    conversation = []
    
    if title:
        conversation.append(f"CONVERSATION TITLE: {title}\n")
    
    conversation.append("=" * 50)
    conversation.append("FULL CONVERSATION TRANSCRIPT")
    conversation.append("=" * 50)
    
    for i, msg in enumerate(messages, 1):
        role = msg['role'].upper()
        content = msg['content'].strip()
        
        # Add message separator
        conversation.append(f"\n--- MESSAGE {i} ({role}) ---")
        conversation.append(content)
    
    return "\n".join(conversation)

def count_tokens(text: str, model: str = config.OPENAI_MODEL) -> int:
    """
    Count tokens in text using tiktoken.
    
    Args:
        text: Text to count tokens for
        model: Model name for tokenizer
        
    Returns:
        Number of tokens
    """
    try:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    except Exception as e:
        print(f"Error counting tokens: {e}")
        # Fallback: rough estimate (4 chars per token)
        return len(text) // 4

def truncate_conversation_if_needed(conversation: str, max_tokens: int = config.MAX_TOKENS_PER_CHAT) -> str:
    """
    Truncate conversation if it exceeds token limit.
    
    Args:
        conversation: Formatted conversation string
        max_tokens: Maximum allowed tokens
        
    Returns:
        Truncated conversation if needed
    """
    current_tokens = count_tokens(conversation)
    
    if current_tokens <= max_tokens:
        return conversation
    
    print(f"Conversation too long ({current_tokens} tokens), truncating to {max_tokens} tokens")
    
    # Split into lines and keep removing from the middle until under limit
    lines = conversation.split('\n')
    
    # Keep title and header
    header_lines = []
    content_lines = []
    
    in_header = True
    for line in lines:
        if in_header and ('=' in line or 'CONVERSATION' in line or 'TITLE:' in line):
            header_lines.append(line)
        else:
            in_header = False
            content_lines.append(line)
    
    # Truncate from the middle, keeping beginning and end
    while count_tokens('\n'.join(header_lines + content_lines)) > max_tokens and len(content_lines) > 20:
        # Remove from middle
        middle_idx = len(content_lines) // 2
        content_lines.pop(middle_idx)
    
    # Add truncation notice
    if len(content_lines) < len(lines) - len(header_lines):
        truncation_notice = [
            "",
            "--- CONVERSATION TRUNCATED FOR LENGTH ---",
            "--- MIDDLE SECTION REMOVED ---",
            ""
        ]
        # Insert notice in middle
        middle_idx = len(content_lines) // 2
        content_lines[middle_idx:middle_idx] = truncation_notice
    
    return '\n'.join(header_lines + content_lines)

def extract_chat_metadata(chat_data: Dict[Any, Any]) -> Dict[str, Any]:
    """
    Extract metadata from chat data.
    
    Args:
        chat_data: Parsed JSON data from chat export
        
    Returns:
        Dictionary with metadata
    """
    return {
        'title': chat_data.get('title', 'Untitled'),
        'created': chat_data.get('created', 0),
        'updated': chat_data.get('updated', 0),
        'message_count': len(chat_data.get('messages', [])),
        'id': chat_data.get('id', '')
    }
