"""
Chat processing utilities for extracting and formatting conversation data
"""
import json
from typing import List, Dict, Any, Optional
import tiktoken
import config
from models import ChatMessage, ChatMetadata, ChatExportData

def extract_messages_from_chat(chat_data: Dict[Any, Any]) -> List[ChatMessage]:
    """
    Extract messages from ChatGPT/Claude JSON format in chronological order.

    Args:
        chat_data: Parsed JSON data from chat export

    Returns:
        List of ChatMessage objects in chronological order
    """
    try:
        # Use Pydantic model for validation
        export_data = ChatExportData(**chat_data)
        return export_data.extract_messages()
    except Exception as e:
        print(f"Error extracting messages with Pydantic: {e}")
        # Fallback to original logic
        return _extract_messages_fallback(chat_data)

def _extract_messages_fallback(chat_data: Dict[Any, Any]) -> List[ChatMessage]:
    """Fallback message extraction without Pydantic validation."""
    messages = []

    if 'messages' not in chat_data:
        print("No messages found in chat data")
        return messages

    # Sort messages by timestamp if available
    raw_messages = chat_data['messages']
    if raw_messages and isinstance(raw_messages[0], dict) and 'timestamp' in raw_messages[0]:
        raw_messages = sorted(raw_messages, key=lambda x: x.get('timestamp', 0))

    for msg in raw_messages:
        try:
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            timestamp = msg.get('timestamp')

            # Skip empty messages or non-conversational roles
            if not content.strip() or role not in ['user', 'assistant', 'system']:
                continue

            message = ChatMessage(
                role=role,
                content=content,
                timestamp=timestamp
            )
            messages.append(message)
        except Exception as e:
            print(f"Warning: Skipping invalid message: {e}")
            continue

    return messages

def format_conversation_for_analysis(messages: List[ChatMessage], title: str = "") -> str:
    """
    Format messages into a readable conversation format for GPT analysis.

    Args:
        messages: List of ChatMessage objects
        title: Optional title for the conversation

    Returns:
        Formatted conversation string
    """
    conversation = []

    if title:
        conversation.append(f"CONVERSATION TITLE: {title}\n")

    conversation.append("=" * 50)
    conversation.append("FULL CONVERSATION TRANSCRIPT")
    conversation.append("=" * 50)

    for i, msg in enumerate(messages, 1):
        role = msg.role.upper()
        content = msg.content.strip()

        # Add message separator
        conversation.append(f"\n--- MESSAGE {i} ({role}) ---")
        conversation.append(content)

    return "\n".join(conversation)

def count_tokens(text: str, model: str = config.OPENAI_MODEL) -> int:
    """
    Count tokens in text using tiktoken.
    
    Args:
        text: Text to count tokens for
        model: Model name for tokenizer
        
    Returns:
        Number of tokens
    """
    try:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    except Exception as e:
        print(f"Error counting tokens: {e}")
        # Fallback: rough estimate (4 chars per token)
        return len(text) // 4

def truncate_conversation_if_needed(conversation: str, max_tokens: int = config.MAX_TOKENS_PER_CHAT) -> str:
    """
    Truncate conversation if it exceeds token limit.
    
    Args:
        conversation: Formatted conversation string
        max_tokens: Maximum allowed tokens
        
    Returns:
        Truncated conversation if needed
    """
    current_tokens = count_tokens(conversation)
    
    if current_tokens <= max_tokens:
        return conversation
    
    print(f"Conversation too long ({current_tokens} tokens), truncating to {max_tokens} tokens")
    
    # Split into lines and keep removing from the middle until under limit
    lines = conversation.split('\n')
    
    # Keep title and header
    header_lines = []
    content_lines = []
    
    in_header = True
    for line in lines:
        if in_header and ('=' in line or 'CONVERSATION' in line or 'TITLE:' in line):
            header_lines.append(line)
        else:
            in_header = False
            content_lines.append(line)
    
    # Truncate from the middle, keeping beginning and end
    while count_tokens('\n'.join(header_lines + content_lines)) > max_tokens and len(content_lines) > 20:
        # Remove from middle
        middle_idx = len(content_lines) // 2
        content_lines.pop(middle_idx)
    
    # Add truncation notice
    if len(content_lines) < len(lines) - len(header_lines):
        truncation_notice = [
            "",
            "--- CONVERSATION TRUNCATED FOR LENGTH ---",
            "--- MIDDLE SECTION REMOVED ---",
            ""
        ]
        # Insert notice in middle
        middle_idx = len(content_lines) // 2
        content_lines[middle_idx:middle_idx] = truncation_notice
    
    return '\n'.join(header_lines + content_lines)

def extract_chat_metadata(chat_data: Dict[Any, Any]) -> ChatMetadata:
    """
    Extract metadata from chat data.

    Args:
        chat_data: Parsed JSON data from chat export

    Returns:
        ChatMetadata object
    """
    try:
        # Use Pydantic model for validation
        export_data = ChatExportData(**chat_data)
        return export_data.to_metadata()
    except Exception as e:
        print(f"Error extracting metadata with Pydantic: {e}")
        # Fallback to basic metadata
        return ChatMetadata(
            title=chat_data.get('title', 'Untitled'),
            created=chat_data.get('created'),
            updated=chat_data.get('updated'),
            message_count=len(chat_data.get('messages', [])),
            id=chat_data.get('id')
        )
