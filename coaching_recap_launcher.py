"""
Coaching Recap Launcher - One-stop interface for all coaching analysis and export features
"""
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_header():
    """Print the application header."""
    print("🤖 COACHING RECAP ANALYSIS SYSTEM")
    print("=" * 50)
    print("Complete coaching conversation analysis with export capabilities")
    print()

def check_analysis_results():
    """Check if analysis results exist."""
    results_file = Path("output/30_day_recap.json")
    return results_file.exists()

def show_main_menu():
    """Show the main menu options."""
    has_results = check_analysis_results()
    
    print("📋 MAIN MENU:")
    print()
    
    print("🔍 ANALYSIS:")
    print("  1. Run 30-day coaching analysis (Optimized)")
    print("  2. Run 7-day coaching analysis")
    print("  3. Run custom timeframe analysis")
    print("  4. Test optimized system (3 files)")
    print()
    
    if has_results:
        print("📊 VIEW RESULTS:")
        print("  5. View analysis results")
        print("  6. Quick summary")
        print()
        
        print("📥 EXPORT OPTIONS:")
        print("  7. Export all formats (Local)")
        print("  8. Export specific format (Local)")
        print("  9. Open web download interface")
        print("  10. Start API server for Custom ChatGPT")
        print()
        
        print("🤖 CUSTOM CHATGPT:")
        print("  11. Get ChatGPT integration instructions")
        print("  12. Test export assistant")
    else:
        print("⚠️  No analysis results found. Run analysis first (options 1-4).")
        print()
    
    print("  0. Exit")
    print()

def run_analysis(days=30):
    """Run the coaching analysis."""
    print(f"🚀 Running {days}-day coaching analysis...")
    print("This will analyze your conversation exports with 95% token savings!")
    print()
    
    try:
        if days == 30:
            subprocess.run([sys.executable, "optimized_coaching_agent.py"], check=True)
        else:
            # For custom days, we'd need to modify the script
            print(f"Custom {days}-day analysis not yet implemented.")
            print("Using 30-day analysis...")
            subprocess.run([sys.executable, "optimized_coaching_agent.py"], check=True)
        
        print()
        print("✅ Analysis complete! You can now view results and export data.")
        
    except subprocess.CalledProcessError:
        print("❌ Analysis failed. Check your configuration and try again.")
    except FileNotFoundError:
        print("❌ Analysis script not found. Make sure optimized_coaching_agent.py exists.")

def view_results():
    """View the analysis results."""
    print("📊 Viewing analysis results...")
    try:
        subprocess.run([sys.executable, "view_results.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Failed to view results.")
    except FileNotFoundError:
        print("❌ Results viewer not found.")

def export_local(format_type="all"):
    """Export using local export manager."""
    print(f"📥 Exporting {format_type} format(s) locally...")
    try:
        subprocess.run([sys.executable, "local_export_manager.py", format_type], check=True)
        print()
        print("✅ Export complete! Files saved to 'exports/' directory.")
    except subprocess.CalledProcessError:
        print("❌ Export failed.")
    except FileNotFoundError:
        print("❌ Local export manager not found.")

def start_api_server():
    """Start the API server for Custom ChatGPT integration."""
    print("🚀 Starting API server for Custom ChatGPT integration...")
    print("The server will run at: http://localhost:5000")
    print("Press Ctrl+C to stop the server.")
    print()
    
    try:
        subprocess.run([sys.executable, "start_api.py"])
    except KeyboardInterrupt:
        print("\n⏹️ Server stopped.")
    except FileNotFoundError:
        print("❌ API server script not found.")

def open_web_interface():
    """Open the web download interface."""
    print("🌐 Opening web download interface...")
    print("Make sure the API server is running first!")
    print()
    
    import webbrowser
    webbrowser.open("http://localhost:5000")

def show_chatgpt_instructions():
    """Show Custom ChatGPT integration instructions."""
    print("🤖 CUSTOM CHATGPT INTEGRATION INSTRUCTIONS")
    print("=" * 55)
    print()
    print("1. Start the API server (option 10)")
    print("2. Go to ChatGPT → Create a GPT")
    print("3. Use the configuration in 'custom_gpt_config.md'")
    print("4. Set base URL to: http://localhost:5000")
    print()
    print("Your Custom ChatGPT will then be able to:")
    print("  • Analyze your coaching conversations")
    print("  • Recommend export formats based on your needs")
    print("  • Provide insights about your coaching journey")
    print("  • Help you download results in multiple formats")
    print()
    print("📖 Full instructions: custom_gpt_config.md")

def test_export_assistant():
    """Test the export assistant."""
    print("🧪 Testing Export Assistant...")
    try:
        subprocess.run([sys.executable, "export_assistant.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Export assistant test failed.")
    except FileNotFoundError:
        print("❌ Export assistant not found.")

def main():
    """Main application loop."""
    print_header()
    
    while True:
        show_main_menu()
        
        try:
            choice = input("Enter your choice (0-12): ").strip()
            print()
            
            if choice == "0":
                print("👋 Goodbye! Your coaching insights await you.")
                break
            elif choice == "1":
                run_analysis(30)
            elif choice == "2":
                run_analysis(7)
            elif choice == "3":
                days = input("Enter number of days to analyze: ").strip()
                try:
                    days = int(days)
                    run_analysis(days)
                except ValueError:
                    print("❌ Invalid number of days.")
            elif choice == "4":
                print("🧪 Testing optimized system...")
                try:
                    subprocess.run([sys.executable, "test_optimized.py"], check=True)
                except:
                    print("❌ Test failed.")
            elif choice == "5":
                view_results()
            elif choice == "6":
                print("📊 Quick Summary:")
                try:
                    # Show quick summary from results
                    import json
                    with open("output/30_day_recap.json", 'r') as f:
                        data = json.load(f)
                    summary = data.get('summary', {})
                    print(f"Period: {summary.get('period', 'Unknown')}")
                    print(f"Total conversations: {summary.get('total_conversations', 0)}")
                    print(f"Success rate: {summary.get('processing_stats', {}).get('success_rate', '0%')}")
                    
                    arc_dist = summary.get('arc_type_distribution', {})
                    if arc_dist:
                        top_arc = max(arc_dist.items(), key=lambda x: x[1])
                        print(f"Top pattern: {top_arc[0].replace('_', ' ').title()} ({top_arc[1]} conversations)")
                except:
                    print("❌ Could not load summary.")
            elif choice == "7":
                export_local("all")
            elif choice == "8":
                format_type = input("Enter format (json/csv/summary/insights): ").strip().lower()
                if format_type in ['json', 'csv', 'summary', 'insights']:
                    export_local(format_type)
                else:
                    print("❌ Invalid format.")
            elif choice == "9":
                open_web_interface()
            elif choice == "10":
                start_api_server()
            elif choice == "11":
                show_chatgpt_instructions()
            elif choice == "12":
                test_export_assistant()
            else:
                print("❌ Invalid choice. Please try again.")
            
            if choice != "0":
                input("\nPress Enter to continue...")
                print()
        
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ An error occurred: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
