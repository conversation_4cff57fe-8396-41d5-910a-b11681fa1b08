{"id": "68483c90-da04-800e-a246-4aa37ddf3b6b", "title": "Avatar Messaging Framework", "created": 1749564561210, "updated": 1749564665506, "currentMessage": "6a5b00b1-8e8e-4a14-af71-70183d76534b", "messages": [{"id": "3f303a3e-b031-4621-8670-6b1d8ab03e0c", "parent": null, "role": "user", "content": "outline the steps for the offer vuilder ", "timestamp": 1749564562436}, {"id": "ff61b3a8-537a-417f-80f3-299d8ba55bac", "parent": "3f303a3e-b031-4621-8670-6b1d8ab03e0c", "role": "tool", "content": "\nPlease remember to search the user's documents if an answer to their question is not contained in the above snippets. You cannot mclick into this file- if needed, you can use msearch to search it for additional information.\n", "timestamp": 1749564573066.233}, {"id": "05ec9065-689a-46ac-9284-0041905f72b4", "parent": "ff61b3a8-537a-417f-80f3-299d8ba55bac", "role": "assistant", "content": "Based on the GrowthWorks materials, here’s a simplified **step-by-step outline** of the *Offer Builder Process*:\n\n---\n\n### 🔹 Step 1: **Identify the Core Problem**\n- Define the", "timestamp": 1749564561738.29}, {"id": "d0d71b9f-2513-4b94-8a8f-c0f6690d2f65", "parent": "05ec9065-689a-46ac-9284-0041905f72b4", "role": "user", "content": "no when you do avatar message  those setps", "timestamp": 1749564624249}, {"id": "6a5b00b1-8e8e-4a14-af71-70183d76534b", "parent": "d0d71b9f-2513-4b94-8a8f-c0f6690d2f65", "role": "assistant", "content": "Got it — you're referring specifically to the steps within the **Offer Builder system**, especially around **Avatar**, **Messaging**, and related elements. Based on GrowthWorks, here's a clear outline of the steps involved:\n\n---\n\n## ✅ GrowthWorks Offer Builder Framework\n\n### 1. **Identify Your Avatar**\n   - Who are you helping? Get specific:\n     - Demographics (age, profession, income)\n     - Psychographics (beliefs, fears, desires)\n     - Where they hang out (online and offline)\n     - What solutions they've tried before\n\n### 2. **Map Their Pain and Desire**\n   - What’s the #1 pain they’re feeling?\n   - What’s the “big domino” belief holding them back?\n   - What transformation are they seeking?\n   - Use the “Before/After” grid: what life looks like now vs. after success.\n\n### 3. **Create Your Million Dollar Message**\n   - Use this format:  \n     **“I help [Avatar] get [Desirable Result] without [Big Frustration] in [Short Timeframe].”**\n   - Include hooks that hit emotional and practical triggers.\n   - Ensure it sounds clear, not clever.\n\n### 4. **Design Your Product Roadmap**\n   - Break down your solution into 3 to 9 major steps/modules.\n   - These become your framework for teaching/delivery.\n   - Each step should resolve a piece of the avatar’s journey.\n\n### 5. **Turn Steps into Cheat Sheets or Lead Magnets**\n   - Create a mini-guide, checklist, or worksheet for one step.\n   - Each can become a lead magnet.\n   - Use for email opt-ins and nurturing.\n\n### 6. **Record an Authority Amplifier Video**\n   - A short video (5–10 mins) explaining your lead magnet\n   - Shows you understand their problem deeply.\n   - Builds trust and leads them toward the next step (e.g., a call or deeper training).\n\n### 7. **Connect Everything in a Funnel**\n   - Opt-in Page → Authority Amplifier → Calendar or CTA Page\n   - Keep design consistent, focus on benefits, not features.\n   - No distractions — one CTA per page.\n\n---\n\nIf you're ready, I can walk you through each of these in more detail, starting with your avatar. Want to begin with that?", "timestamp": 1749564623369.944}]}