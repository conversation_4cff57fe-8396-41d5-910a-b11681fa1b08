"""
Test script for the Coaching Recap Agent
"""
import os
from pathlib import Path
from coaching_recap_agent import CoachingRecapAgent
from file_utils import get_recent_files
import config

def test_file_discovery():
    """Test file discovery functionality."""
    print("🔍 Testing file discovery...")
    
    recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, 30)
    print(f"Found {len(recent_files)} files modified in the last 30 days")
    
    for file_path in recent_files[:5]:  # Show first 5
        file_name = Path(file_path).name
        print(f"  • {file_name}")
    
    if len(recent_files) > 5:
        print(f"  ... and {len(recent_files) - 5} more files")
    
    return len(recent_files) > 0

def test_single_chat_processing():
    """Test processing a single chat file."""
    print("\n📄 Testing single chat processing...")
    
    recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, 30)
    if not recent_files:
        print("❌ No recent files found to test")
        return False
    
    # Test with the first file
    test_file = recent_files[0]
    file_name = Path(test_file).name
    print(f"Testing with: {file_name}")
    
    try:
        agent = CoachingRecapAgent()
        result = agent.process_single_chat(test_file)
        
        if isinstance(result, dict) and "error" in result:
            print(f"❌ Error processing file: {result['error']}")
            return False
        
        print("✅ Successfully processed single chat")
        print(f"   Arc type: {result.arc_type}")
        print(f"   Quality: {result.coaching_quality}/10")
        print(f"   Themes: {', '.join(result.key_themes[:3])}")
        return True
        
    except Exception as e:
        print(f"❌ Exception during processing: {e}")
        return False

def test_environment_setup():
    """Test environment and configuration."""
    print("🔧 Testing environment setup...")
    
    # Check API key
    if not config.OPENAI_API_KEY:
        print("❌ OPENAI_API_KEY not found in environment")
        print("   Please create a .env file with your OpenAI API key")
        return False
    else:
        print("✅ OpenAI API key found")
    
    # Check directories
    if not Path(config.CHAT_EXPORTS_DIR).exists():
        print(f"❌ ChatExports directory not found: {config.CHAT_EXPORTS_DIR}")
        return False
    else:
        print("✅ ChatExports directory exists")
    
    # Check for JSON files
    json_files = list(Path(config.CHAT_EXPORTS_DIR).glob("*.json"))
    if not json_files:
        print("❌ No JSON files found in ChatExports directory")
        return False
    else:
        print(f"✅ Found {len(json_files)} JSON files")
    
    return True

def main():
    """Run all tests."""
    print("🤖 Coaching Recap Agent - Test Suite")
    print("=" * 50)
    
    # Test environment
    if not test_environment_setup():
        print("\n❌ Environment setup failed. Please check configuration.")
        return
    
    # Test file discovery
    if not test_file_discovery():
        print("\n❌ No recent files found. Check your ChatExports directory.")
        return
    
    # Test single chat processing (requires API key)
    if config.OPENAI_API_KEY and config.OPENAI_API_KEY != "your_openai_api_key_here":
        if test_single_chat_processing():
            print("\n✅ All tests passed! The agent is ready to use.")
        else:
            print("\n❌ Single chat processing failed. Check your API key and network connection.")
    else:
        print("\n⚠️  Skipping API tests - please set your OpenAI API key in .env file")
        print("✅ Basic functionality tests passed!")
    
    print("\n🚀 To run the full analysis, execute:")
    print("   python coaching_recap_agent.py")

if __name__ == "__main__":
    main()
