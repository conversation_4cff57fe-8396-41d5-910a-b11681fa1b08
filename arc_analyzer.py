"""
Arc analysis using OpenAI GPT for coaching conversation analysis
"""
import json
from datetime import datetime
from typing import Dict, Any, Optional
from openai import OpenAI
import config
from models import CoachingAnalysis, TurningPoint

class ArcAnalyzer:
    def __init__(self):
        """Initialize the Arc Analyzer with OpenAI client."""
        if not config.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
        
        self.client = OpenAI(api_key=config.OPENAI_API_KEY)
    
    def create_analysis_prompt(self, conversation: str) -> str:
        """
        Create a structured prompt for GPT to analyze the coaching arc.
        
        Args:
            conversation: Formatted conversation string
            
        Returns:
            Analysis prompt for GPT
        """
        prompt = f"""
You are an expert coaching conversation analyst. Read this full conversation and identify the coaching arc with deep insight.

CONVERSATION TO ANALYZE:
{conversation}

ANALYSIS INSTRUCTIONS:
Analyze this conversation as a complete narrative arc and provide a structured JSON response with the following fields:

1. "start_point": What was the initial concern, question, confusion, or goal that began this conversation?

2. "turning_points": List 2-4 key moments where the conversation shifted direction, new insights emerged, or breakthroughs happened. For each turning point, include:
   - "moment": Brief description of what happened
   - "insight": What realization or shift occurred
   - "significance": Why this was important to the overall arc

3. "resolution": What final insight, decision, clarity, or resolution was reached by the end? What changed from start to finish?

4. "open_loops": What questions, tensions, or threads remain unresolved or need further exploration?

5. "arc_type": Classify this conversation into one of these coaching arc types:
   - "emotional_breakthrough": Processing emotions, healing, releasing blocks
   - "creative_clarity": Gaining clarity on creative projects, vision, or expression  
   - "decision_making_loop": Working through decisions, choices, or next steps
   - "structural_tension": Addressing systemic patterns, structures, or recurring issues
   - "identity_integration": Exploring identity, roles, or self-concept
   - "resistance_breakdown": Working through resistance, procrastination, or avoidance
   - "visionary_expansion": Expanding vision, possibilities, or future thinking
   - "collaboration_wound": Processing relationship, team, or collaboration challenges
   - "sovereignty_reclaim": Reclaiming personal power, boundaries, or autonomy
   - "other": If none of the above fit, specify what type it is

6. "key_themes": List 3-5 major themes that ran through this conversation

7. "coaching_quality": Rate the coaching effectiveness (1-10) and briefly explain why

8. "integration_potential": What would be most valuable to remember or act on from this conversation?

Respond ONLY with valid JSON in this exact format:
{{
  "start_point": "...",
  "turning_points": [
    {{
      "moment": "...",
      "insight": "...", 
      "significance": "..."
    }}
  ],
  "resolution": "...",
  "open_loops": ["...", "..."],
  "arc_type": "...",
  "key_themes": ["...", "...", "..."],
  "coaching_quality": 8,
  "integration_potential": "..."
}}
"""
        return prompt
    
    def analyze_conversation(self, conversation: str, title: str = "") -> Optional[CoachingAnalysis]:
        """
        Send conversation to GPT for arc analysis.

        Args:
            conversation: Formatted conversation string
            title: Optional conversation title

        Returns:
            CoachingAnalysis object or None if failed
        """
        try:
            prompt = self.create_analysis_prompt(conversation)
            
            print(f"Analyzing conversation: {title}")
            print(f"Prompt length: {len(prompt)} characters")
            
            response = self.client.chat.completions.create(
                model=config.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert coaching conversation analyst. Always respond with valid JSON only."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=2000
            )
            
            # Extract and parse the response
            analysis_text = response.choices[0].message.content.strip()
            
            # Clean up the response (remove any markdown formatting)
            if analysis_text.startswith('```json'):
                analysis_text = analysis_text[7:]
            if analysis_text.endswith('```'):
                analysis_text = analysis_text[:-3]
            
            analysis_text = analysis_text.strip()
            
            # Parse JSON
            analysis_data = json.loads(analysis_text)

            # Create Pydantic model
            analysis = CoachingAnalysis(**analysis_data)

            # Add metadata
            analysis.metadata = {
                'title': title,
                'analyzed_at': datetime.now().isoformat(),
                'model_used': config.OPENAI_MODEL,
                'tokens_used': response.usage.total_tokens if response.usage else 0
            }

            return analysis
            
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            print(f"Raw response: {analysis_text[:500]}...")
            return None
        except Exception as e:
            print(f"Error analyzing conversation: {e}")
            return None
    
    def batch_analyze_conversations(self, conversations: Dict[str, str]) -> Dict[str, CoachingAnalysis]:
        """
        Analyze multiple conversations and return results.
        
        Args:
            conversations: Dictionary mapping file paths to conversation strings
            
        Returns:
            Dictionary mapping file paths to analysis results
        """
        results = {}
        
        for file_path, conversation in conversations.items():
            title = file_path.split('/')[-1].replace('.json', '')
            analysis = self.analyze_conversation(conversation, title)
            
            if analysis:
                results[file_path] = analysis
                print(f"✓ Successfully analyzed: {title}")
            else:
                print(f"✗ Failed to analyze: {title}")
        
        return results
