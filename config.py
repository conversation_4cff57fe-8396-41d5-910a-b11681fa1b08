"""
Configuration settings for the Coaching Recap Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OpenAI Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = "gpt-4o"

# File paths
CHAT_EXPORTS_DIR = "./ChatExports"
OUTPUT_DIR = "./output"
RECAP_FILENAME = "30_day_recap.json"

# Processing settings
DAYS_LOOKBACK = 30
MAX_TOKENS_PER_CHAT = 12000  # Leave room for prompt and response
TOKEN_BUFFER = 2000  # Buffer for prompt and response tokens

# Supported file extensions
SUPPORTED_EXTENSIONS = ['.json']

# Arc analysis categories
ARC_TYPES = [
    "emotional_breakthrough",
    "creative_clarity", 
    "decision_making_loop",
    "structural_tension",
    "identity_integration",
    "resistance_breakdown",
    "visionary_expansion",
    "collaboration_wound",
    "sovereignty_reclaim",
    "other"
]
