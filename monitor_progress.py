"""
Monitor the progress of the coaching recap agent
"""
import time
import os
from pathlib import Path

def monitor_progress():
    """Monitor the progress of the analysis."""
    print("🔍 Monitoring Coaching Recap Agent Progress...")
    print("=" * 50)
    
    output_dir = Path("output")
    
    while True:
        # Check if output directory exists and has files
        if output_dir.exists():
            json_files = list(output_dir.glob("*.json"))
            if json_files:
                print(f"✅ Analysis complete! Found {len(json_files)} result files.")
                
                # Show the main results file
                main_file = output_dir / "30_day_recap.json"
                if main_file.exists():
                    print(f"📊 Main results file: {main_file}")
                    print(f"📁 File size: {main_file.stat().st_size / 1024:.1f} KB")
                
                break
            else:
                print("⏳ Analysis in progress... (output directory exists but no results yet)")
        else:
            print("⏳ Analysis in progress... (no output directory yet)")
        
        # Wait before checking again
        time.sleep(10)
    
    print("\n🎉 Analysis completed! You can now view the results.")
    print("\n📋 Next steps:")
    print("1. Check the output/30_day_recap.json file for full results")
    print("2. Run 'python view_results.py' to see a formatted summary")

if __name__ == "__main__":
    try:
        monitor_progress()
    except KeyboardInterrupt:
        print("\n⏹️  Monitoring stopped by user.")
        print("The analysis may still be running in the background.")
