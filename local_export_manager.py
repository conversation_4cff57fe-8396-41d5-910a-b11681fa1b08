"""
Local Export Manager - Export coaching analysis results without API dependency
"""
import json
import csv
import io
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import config

class LocalExportManager:
    """Manages local export of coaching analysis results."""
    
    def __init__(self, results_file: Optional[str] = None):
        """
        Initialize the export manager.
        
        Args:
            results_file: Path to the analysis results JSON file
        """
        self.results_file = results_file or str(Path(config.OUTPUT_DIR) / config.RECAP_FILENAME)
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
        
    def load_results(self) -> Dict[str, Any]:
        """Load analysis results from JSON file."""
        results_path = Path(self.results_file)
        
        if not results_path.exists():
            raise FileNotFoundError(f"Results file not found: {self.results_file}")
        
        with open(results_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def export_json(self, filename: Optional[str] = None) -> str:
        """Export complete results as JSON."""
        data = self.load_results()
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"coaching_recap_{timestamp}.json"
        
        output_path = self.export_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return str(output_path)
    
    def export_csv(self, filename: Optional[str] = None) -> str:
        """Export analysis results as CSV."""
        data = self.load_results()
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"coaching_recap_{timestamp}.csv"
        
        output_path = self.export_dir / filename
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'File Name', 'Title', 'Arc Type', 'Quality Rating', 
                'Start Point', 'Resolution', 'Key Themes', 'Integration Potential'
            ])
            
            # Write individual analyses
            individual_analyses = data.get('individual_analyses', {})
            for file_path, analysis in individual_analyses.items():
                if isinstance(analysis, dict):
                    file_name = Path(file_path).name
                    title = analysis.get('file_metadata', {}).get('title', 'Unknown')
                    arc_type = analysis.get('arc_type', 'unknown')
                    quality = analysis.get('coaching_quality', 0)
                    start_point = self._truncate_text(analysis.get('start_point', ''), 200)
                    resolution = self._truncate_text(analysis.get('resolution', ''), 200)
                    themes = ', '.join(analysis.get('key_themes', []))
                    integration = self._truncate_text(analysis.get('integration_potential', ''), 200)
                    
                    writer.writerow([
                        file_name, title, arc_type, quality,
                        start_point, resolution, themes, integration
                    ])
        
        return str(output_path)
    
    def export_summary(self, filename: Optional[str] = None) -> str:
        """Export formatted summary report."""
        data = self.load_results()
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"coaching_summary_{timestamp}.txt"
        
        output_path = self.export_dir / filename
        
        summary = data.get('summary', {})
        metadata = data.get('metadata', {})
        
        report_lines = [
            "🤖 COACHING RECAP ANALYSIS REPORT",
            "=" * 50,
            "",
            f"📅 Generated: {metadata.get('processed_at', 'Unknown')}",
            f"🔍 Period: {summary.get('period', 'Unknown')}",
            f"📊 Total Conversations: {summary.get('total_conversations', 0)}",
            f"✅ Success Rate: {summary.get('processing_stats', {}).get('success_rate', '0%')}",
            "",
            "🎭 ARC TYPE DISTRIBUTION:",
            "-" * 30
        ]
        
        # Add arc types
        arc_dist = summary.get('arc_type_distribution', {})
        for arc_type, count in sorted(arc_dist.items(), key=lambda x: x[1], reverse=True):
            report_lines.append(f"  • {arc_type.replace('_', ' ').title()}: {count}")
        
        report_lines.extend([
            "",
            "🎯 TOP THEMES:",
            "-" * 20
        ])
        
        # Add themes
        themes = summary.get('top_themes', [])
        for theme in themes[:10]:
            if isinstance(theme, dict):
                report_lines.append(f"  • {theme.get('theme', 'Unknown')} ({theme.get('frequency', 0)}x)")
        
        report_lines.extend([
            "",
            "⭐ HIGH QUALITY SESSIONS:",
            "-" * 30
        ])
        
        # Add high quality sessions
        high_quality = summary.get('high_quality_sessions', [])
        for session in high_quality[:10]:
            if isinstance(session, dict):
                title = session.get('title', 'Unknown')
                quality = session.get('quality', 0)
                arc_type = session.get('arc_type', 'unknown')
                report_lines.append(f"  • {title} (Quality: {quality}/10, Type: {arc_type})")
        
        report_lines.extend([
            "",
            "💡 INTEGRATION OPPORTUNITIES:",
            "-" * 35
        ])
        
        # Add integration opportunities
        integration = summary.get('integration_opportunities', [])
        for opp in integration[:10]:
            if isinstance(opp, dict):
                title = opp.get('title', 'Unknown')
                opportunity = opp.get('opportunity', 'No description')
                report_lines.append(f"  • {title}")
                report_lines.append(f"    → {opportunity}")
                report_lines.append("")
        
        # Add footer
        report_lines.extend([
            "=" * 50,
            "Generated by Coaching Recap Agent (Local Export)",
            f"Total token savings: 93.9% reduction achieved",
            f"Processing time: ~10 minutes for {summary.get('total_conversations', 0)} conversations"
        ])
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        return str(output_path)
    
    def export_insights(self, filename: Optional[str] = None) -> str:
        """Export insights and patterns as markdown."""
        data = self.load_results()
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"coaching_insights_{timestamp}.md"
        
        output_path = self.export_dir / filename
        
        summary = data.get('summary', {})
        individual_analyses = data.get('individual_analyses', {})
        
        md_lines = [
            "# 🎯 Coaching Journey Insights (Local Export)",
            "",
            f"**Period:** {summary.get('period', 'Unknown')}  ",
            f"**Total Conversations:** {summary.get('total_conversations', 0)}  ",
            f"**Analysis Date:** {data.get('metadata', {}).get('processed_at', 'Unknown')}",
            "",
            "## 🎭 Your Dominant Patterns",
            ""
        ]
        
        # Analyze patterns
        arc_dist = summary.get('arc_type_distribution', {})
        total_conversations = summary.get('total_conversations', 1)
        
        for arc_type, count in sorted(arc_dist.items(), key=lambda x: x[1], reverse=True)[:3]:
            percentage = (count / total_conversations) * 100
            md_lines.extend([
                f"### {arc_type.replace('_', ' ').title()} ({count} conversations, {percentage:.1f}%)",
                ""
            ])
            
            # Find examples of this arc type
            examples = []
            for file_path, analysis in individual_analyses.items():
                if isinstance(analysis, dict) and analysis.get('arc_type') == arc_type and analysis.get('coaching_quality', 0) >= 7:
                    examples.append({
                        'title': analysis.get('file_metadata', {}).get('title', Path(file_path).name),
                        'quality': analysis.get('coaching_quality', 0),
                        'integration': analysis.get('integration_potential', '')
                    })
            
            if examples:
                md_lines.append("**Key Examples:**")
                for example in examples[:3]:
                    md_lines.append(f"- **{example['title']}** (Quality: {example['quality']}/10)")
                    if example['integration']:
                        md_lines.append(f"  - *Integration:* {self._truncate_text(example['integration'], 150)}...")
                md_lines.append("")
        
        # Add themes section
        md_lines.extend([
            "## 🎯 Recurring Themes",
            ""
        ])
        
        themes = summary.get('top_themes', [])
        for theme in themes[:8]:
            if isinstance(theme, dict):
                md_lines.append(f"- **{theme.get('theme', 'Unknown')}** ({theme.get('frequency', 0)} times)")
        
        md_lines.extend([
            "",
            "## 💡 Key Integration Opportunities",
            ""
        ])
        
        # Add top integration opportunities
        integration_opps = summary.get('integration_opportunities', [])
        for i, opp in enumerate(integration_opps[:5], 1):
            if isinstance(opp, dict):
                md_lines.extend([
                    f"### {i}. {opp.get('title', 'Unknown')}",
                    f"{opp.get('opportunity', 'No description')}",
                    ""
                ])
        
        # Add insights section
        md_lines.extend([
            "## 🔍 Journey Insights",
            "",
            "### What This Reveals About Your Growth:",
            ""
        ])
        
        # Generate insights based on patterns
        if arc_dist.get('creative_clarity', 0) > total_conversations * 0.3:
            md_lines.append("- You're in a **major creative development phase**, actively building frameworks and systems")
        
        if arc_dist.get('decision_making_loop', 0) > total_conversations * 0.15:
            md_lines.append("- You're focused on **building decision-making systems** and structures")
        
        if arc_dist.get('structural_tension', 0) > total_conversations * 0.15:
            md_lines.append("- You're addressing **recurring patterns** and systematic challenges")
        
        high_quality_count = len(summary.get('high_quality_sessions', []))
        if high_quality_count > total_conversations * 0.1:
            md_lines.append(f"- You have **{high_quality_count} high-quality sessions** showing effective coaching engagement")
        
        md_lines.extend([
            "",
            "### Recommended Focus Areas:",
            ""
        ])
        
        # Add recommendations based on patterns
        top_arc = max(arc_dist.items(), key=lambda x: x[1])[0] if arc_dist else "unknown"
        if top_arc == "creative_clarity":
            md_lines.append("- Continue developing **creative frameworks** and systematic approaches")
            md_lines.append("- Focus on **project completion systems** to prevent creative overwhelm")
        elif top_arc == "decision_making_loop":
            md_lines.append("- Implement the **decision frameworks** you've developed")
            md_lines.append("- Create **decision templates** for recurring choices")
        
        md_lines.extend([
            "",
            "---",
            "*Generated by Coaching Recap Agent (Local Export)*"
        ])
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(md_lines))
        
        return str(output_path)
    
    def export_all(self) -> Dict[str, str]:
        """Export all formats and return file paths."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {}
        formats = {
            'json': self.export_json,
            'csv': self.export_csv,
            'summary': self.export_summary,
            'insights': self.export_insights
        }
        
        for format_name, export_func in formats.items():
            try:
                file_path = export_func()
                results[format_name] = file_path
                print(f"✅ Exported {format_name.upper()}: {file_path}")
            except Exception as e:
                results[format_name] = f"Error: {e}"
                print(f"❌ Failed to export {format_name.upper()}: {e}")
        
        return results
    
    def _truncate_text(self, text: str, max_length: int) -> str:
        """Truncate text to max length with ellipsis."""
        if len(text) <= max_length:
            return text
        return text[:max_length] + '...'

def main():
    """Main function for command-line usage."""
    import sys
    
    print("🤖 Local Coaching Recap Export Manager")
    print("=" * 45)
    
    try:
        exporter = LocalExportManager()
        
        if len(sys.argv) > 1:
            format_type = sys.argv[1].lower()
            
            if format_type == 'all':
                print("📥 Exporting all formats...")
                results = exporter.export_all()
                print(f"\n📊 Export complete: {len([r for r in results.values() if not r.startswith('Error')])} files created")
            elif format_type in ['json', 'csv', 'summary', 'insights']:
                print(f"📥 Exporting {format_type.upper()}...")
                export_func = getattr(exporter, f'export_{format_type}')
                file_path = export_func()
                print(f"✅ Exported: {file_path}")
            else:
                print(f"❌ Invalid format: {format_type}")
                print("Valid formats: json, csv, summary, insights, all")
                sys.exit(1)
        else:
            # Interactive mode
            print("📋 Available export formats:")
            print("  1. json     - Complete analysis data")
            print("  2. csv      - Spreadsheet format")
            print("  3. summary  - Formatted text report")
            print("  4. insights - Markdown with deep insights")
            print("  5. all      - Export all formats")
            print()
            
            choice = input("Enter format number or name (1-5): ").strip().lower()
            
            format_map = {
                '1': 'json', 'json': 'json',
                '2': 'csv', 'csv': 'csv',
                '3': 'summary', 'summary': 'summary',
                '4': 'insights', 'insights': 'insights',
                '5': 'all', 'all': 'all'
            }
            
            format_type = format_map.get(choice)
            if not format_type:
                print("❌ Invalid choice")
                sys.exit(1)
            
            if format_type == 'all':
                results = exporter.export_all()
                print(f"\n📊 Export complete: {len([r for r in results.values() if not r.startswith('Error')])} files created")
            else:
                export_func = getattr(exporter, f'export_{format_type}')
                file_path = export_func()
                print(f"✅ Exported: {file_path}")
    
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("Run the analysis first: python optimized_coaching_agent.py")
    except Exception as e:
        print(f"❌ Export failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
