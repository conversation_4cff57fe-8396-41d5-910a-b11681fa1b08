"""
Local Coaching Recap Agent - Processes <PERSON><PERSON><PERSON><PERSON>/<PERSON> transcripts to extract coaching arcs
"""
import os
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# Import our modules
import config
from file_utils import get_recent_files, load_json_file, save_json_file, ensure_output_directory
from chat_processor import extract_messages_from_chat, format_conversation_for_analysis, truncate_conversation_if_needed, extract_chat_metadata
from arc_analyzer import ArcAnalyzer

# Augment SDK import (will be available when installed)
try:
    from augment import action
    AUGMENT_AVAILABLE = True
except ImportError:
    print("Augment SDK not available - running in standalone mode")
    AUGMENT_AVAILABLE = False
    # Create a dummy decorator for standalone mode
    def action(func):
        return func

class CoachingRecapAgent:
    """Main agent class for processing coaching conversations."""
    
    def __init__(self):
        """Initialize the agent."""
        self.analyzer = ArcAnalyzer()
        ensure_output_directory()
    
    def process_single_chat(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single chat file and return analysis.
        
        Args:
            file_path: Path to the JSON chat file
            
        Returns:
            Dictionary with analysis results
        """
        print(f"\nProcessing: {Path(file_path).name}")
        
        # Load the chat data
        chat_data = load_json_file(file_path)
        if not chat_data:
            return {"error": f"Failed to load {file_path}"}
        
        # Extract metadata
        metadata = extract_chat_metadata(chat_data)
        
        # Extract and format messages
        messages = extract_messages_from_chat(chat_data)
        if not messages:
            return {"error": f"No messages found in {file_path}"}
        
        # Format conversation for analysis
        conversation = format_conversation_for_analysis(messages, metadata['title'])
        
        # Truncate if needed
        conversation = truncate_conversation_if_needed(conversation)
        
        # Analyze the conversation
        analysis = self.analyzer.analyze_conversation(conversation, metadata['title'])
        
        if analysis:
            # Add file metadata to analysis
            analysis['_file_metadata'] = {
                'file_path': file_path,
                'file_name': Path(file_path).name,
                **metadata
            }
            return analysis
        else:
            return {"error": f"Failed to analyze {file_path}"}
    
    def process_recent_chats(self, days_back: int = config.DAYS_LOOKBACK) -> Dict[str, Any]:
        """
        Process all recent chat files and return comprehensive analysis.
        
        Args:
            days_back: Number of days to look back
            
        Returns:
            Dictionary with all analysis results and summary
        """
        print(f"🔍 Scanning for files modified in the last {days_back} days...")
        
        # Get recent files
        recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, days_back)
        
        if not recent_files:
            print(f"No files found modified in the last {days_back} days")
            return {"error": "No recent files found"}
        
        print(f"Found {len(recent_files)} recent files to process")
        
        # Process each file
        results = {}
        successful_analyses = []
        failed_analyses = []
        
        for file_path in recent_files:
            try:
                analysis = self.process_single_chat(file_path)
                results[file_path] = analysis
                
                if "error" in analysis:
                    failed_analyses.append(file_path)
                else:
                    successful_analyses.append(file_path)
                    
            except Exception as e:
                error_msg = f"Exception processing {file_path}: {e}"
                print(error_msg)
                results[file_path] = {"error": error_msg}
                failed_analyses.append(file_path)
        
        # Create summary
        summary = self.create_summary(results, successful_analyses, failed_analyses, days_back)
        
        # Prepare final output
        output = {
            "summary": summary,
            "individual_analyses": results,
            "metadata": {
                "processed_at": datetime.now().isoformat(),
                "days_lookback": days_back,
                "total_files_found": len(recent_files),
                "successful_analyses": len(successful_analyses),
                "failed_analyses": len(failed_analyses)
            }
        }
        
        # Save results
        output_path = os.path.join(config.OUTPUT_DIR, config.RECAP_FILENAME)
        if save_json_file(output, output_path):
            print(f"✅ Results saved to: {output_path}")
        
        return output
    
    def create_summary(self, results: Dict[str, Any], successful: List[str], failed: List[str], days_back: int) -> Dict[str, Any]:
        """Create a summary of all analyses."""
        
        # Extract successful analyses
        analyses = [results[path] for path in successful if "error" not in results[path]]
        
        if not analyses:
            return {"message": "No successful analyses to summarize"}
        
        # Aggregate insights
        arc_types = {}
        all_themes = []
        high_quality_sessions = []
        integration_opportunities = []
        
        for analysis in analyses:
            # Count arc types
            arc_type = analysis.get('arc_type', 'unknown')
            arc_types[arc_type] = arc_types.get(arc_type, 0) + 1
            
            # Collect themes
            themes = analysis.get('key_themes', [])
            all_themes.extend(themes)
            
            # High quality sessions (8+ rating)
            quality = analysis.get('coaching_quality', 0)
            if quality >= 8:
                high_quality_sessions.append({
                    'title': analysis.get('_file_metadata', {}).get('title', 'Unknown'),
                    'quality': quality,
                    'arc_type': arc_type
                })
            
            # Integration opportunities
            integration = analysis.get('integration_potential', '')
            if integration:
                integration_opportunities.append({
                    'title': analysis.get('_file_metadata', {}).get('title', 'Unknown'),
                    'opportunity': integration
                })
        
        # Count theme frequency
        theme_counts = {}
        for theme in all_themes:
            theme_counts[theme] = theme_counts.get(theme, 0) + 1
        
        # Sort by frequency
        top_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "period": f"Last {days_back} days",
            "total_conversations": len(analyses),
            "arc_type_distribution": arc_types,
            "top_themes": [{"theme": theme, "frequency": count} for theme, count in top_themes],
            "high_quality_sessions": high_quality_sessions,
            "integration_opportunities": integration_opportunities,
            "processing_stats": {
                "successful": len(successful),
                "failed": len(failed),
                "success_rate": f"{len(successful)/(len(successful)+len(failed))*100:.1f}%" if (successful or failed) else "0%"
            }
        }

# Create global agent instance
agent = CoachingRecapAgent()

@action
def recap_last_30_days() -> Dict[str, Any]:
    """
    Augment action to analyze coaching conversations from the last 30 days.
    
    Returns:
        Comprehensive analysis of recent coaching conversations including:
        - Individual conversation arcs
        - Summary of patterns and themes
        - Integration opportunities
        - High-quality session highlights
    """
    return agent.process_recent_chats(30)

@action  
def recap_last_7_days() -> Dict[str, Any]:
    """
    Augment action to analyze coaching conversations from the last 7 days.
    
    Returns:
        Analysis of recent coaching conversations from the past week
    """
    return agent.process_recent_chats(7)

@action
def analyze_specific_chat(file_name: str) -> Dict[str, Any]:
    """
    Augment action to analyze a specific chat file.
    
    Args:
        file_name: Name of the JSON file in ChatExports directory
        
    Returns:
        Analysis of the specific conversation
    """
    file_path = os.path.join(config.CHAT_EXPORTS_DIR, file_name)
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_name}"}
    
    return agent.process_single_chat(file_path)

def main():
    """Main function for standalone execution."""
    print("🤖 Coaching Recap Agent")
    print("=" * 50)
    
    if not config.OPENAI_API_KEY:
        print("❌ Error: OPENAI_API_KEY not found in environment variables")
        print("Please create a .env file with your OpenAI API key:")
        print("OPENAI_API_KEY=your_api_key_here")
        return
    
    # Run the 30-day analysis
    results = recap_last_30_days()
    
    if "error" in results:
        print(f"❌ Error: {results['error']}")
        return
    
    # Print summary
    summary = results.get("summary", {})
    print(f"\n📊 SUMMARY - {summary.get('period', 'Unknown period')}")
    print(f"Total conversations analyzed: {summary.get('total_conversations', 0)}")
    
    if summary.get('arc_type_distribution'):
        print("\n🎭 Arc Types:")
        for arc_type, count in summary['arc_type_distribution'].items():
            print(f"  • {arc_type}: {count}")
    
    if summary.get('top_themes'):
        print("\n🎯 Top Themes:")
        for theme_data in summary['top_themes'][:5]:
            print(f"  • {theme_data['theme']} ({theme_data['frequency']}x)")
    
    if summary.get('high_quality_sessions'):
        print("\n⭐ High Quality Sessions:")
        for session in summary['high_quality_sessions'][:3]:
            print(f"  • {session['title']} (Quality: {session['quality']}/10)")
    
    print(f"\n✅ Complete results saved to: {os.path.join(config.OUTPUT_DIR, config.RECAP_FILENAME)}")

if __name__ == "__main__":
    main()
