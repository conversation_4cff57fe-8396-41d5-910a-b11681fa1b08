"""
Command-line script to download coaching recap exports
"""
import requests
import sys
from pathlib import Path
from datetime import datetime

API_BASE = "http://localhost:5000"

def download_file(format_type, output_dir="downloads"):
    """Download a specific export format."""
    try:
        print(f"📥 Downloading {format_type.upper()} export...")
        
        # Create downloads directory
        Path(output_dir).mkdir(exist_ok=True)
        
        # Make request
        response = requests.get(f"{API_BASE}/export/{format_type}")
        
        if response.status_code != 200:
            error_data = response.json() if response.headers.get('content-type') == 'application/json' else {}
            error_msg = error_data.get('error', f'HTTP {response.status_code}')
            print(f"❌ Download failed: {error_msg}")
            return False
        
        # Get filename from headers or create default
        content_disposition = response.headers.get('Content-Disposition', '')
        if 'filename=' in content_disposition:
            filename = content_disposition.split('filename=')[1].strip('"')
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extensions = {'json': 'json', 'csv': 'csv', 'summary': 'txt', 'insights': 'md'}
            ext = extensions.get(format_type, 'txt')
            filename = f"coaching_recap_{timestamp}.{ext}"
        
        # Save file
        output_path = Path(output_dir) / filename
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ Downloaded: {output_path}")
        print(f"   Size: {len(response.content):,} bytes")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running:")
        print("   python start_api.py")
        return False
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

def download_all(output_dir="downloads"):
    """Download all export formats."""
    formats = ['json', 'csv', 'summary', 'insights']
    successful = 0
    
    print("🚀 Downloading all coaching recap exports...")
    print("=" * 50)
    
    for format_type in formats:
        if download_file(format_type, output_dir):
            successful += 1
        print()
    
    print(f"📊 Download complete: {successful}/{len(formats)} files downloaded")
    if successful > 0:
        print(f"📁 Files saved to: {Path(output_dir).absolute()}")

def check_api_health():
    """Check if the API is running."""
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Status: {data.get('message', 'Running')}")
            return True
        else:
            print(f"⚠️ API returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API not accessible. Start the server with:")
        print("   python start_api.py")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Main function."""
    print("🤖 Coaching Recap Export Downloader")
    print("=" * 40)
    
    # Check API health first
    if not check_api_health():
        sys.exit(1)
    
    print()
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        format_type = sys.argv[1].lower()
        valid_formats = ['json', 'csv', 'summary', 'insights', 'all']
        
        if format_type not in valid_formats:
            print(f"❌ Invalid format: {format_type}")
            print(f"Valid formats: {', '.join(valid_formats)}")
            sys.exit(1)
        
        if format_type == 'all':
            download_all()
        else:
            download_file(format_type)
    else:
        # Interactive mode
        print("📋 Available export formats:")
        print("  1. json     - Complete analysis data")
        print("  2. csv      - Spreadsheet format")
        print("  3. summary  - Formatted text report")
        print("  4. insights - Markdown with deep insights")
        print("  5. all      - Download all formats")
        print()
        
        choice = input("Enter format number or name (1-5): ").strip().lower()
        
        format_map = {
            '1': 'json', 'json': 'json',
            '2': 'csv', 'csv': 'csv',
            '3': 'summary', 'summary': 'summary',
            '4': 'insights', 'insights': 'insights',
            '5': 'all', 'all': 'all'
        }
        
        format_type = format_map.get(choice)
        if not format_type:
            print("❌ Invalid choice")
            sys.exit(1)
        
        if format_type == 'all':
            download_all()
        else:
            download_file(format_type)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Download cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
