"""
Optimized Coaching Recap Agent - Fast batch processing with minimal AI usage
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
import concurrent.futures
from threading import Lock

# Import our modules
import config
from file_utils import get_recent_files, load_json_file, save_json_file, ensure_output_directory
from chat_processor import extract_messages_from_chat, extract_chat_metadata
from arc_analyzer import ArcAnalyzer
from smart_conversation_scanner import SmartConversationScanner
from models import CoachingAnalysis, CoachingRecapResult, AnalysisSummary, RecapMetadata

class OptimizedCoachingAgent:
    """Optimized agent with smart scanning and batch processing."""
    
    def __init__(self, batch_size: int = 10, max_workers: int = 5):
        """
        Initialize the optimized agent.
        
        Args:
            batch_size: Number of conversations to process in parallel
            max_workers: Maximum number of concurrent API calls
        """
        self.analyzer = ArcAnalyzer()
        self.scanner = SmartConversationScanner()
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.results_lock = Lock()
        ensure_output_directory()
    
    def process_single_chat_optimized(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single chat with optimized scanning.
        
        Args:
            file_path: Path to the JSON chat file
            
        Returns:
            Analysis result or error dict
        """
        try:
            print(f"📄 Processing: {Path(file_path).name}")
            
            # Load and extract data
            chat_data = load_json_file(file_path)
            if not chat_data:
                return {"error": f"Failed to load {file_path}"}
            
            metadata = extract_chat_metadata(chat_data)
            messages = extract_messages_from_chat(chat_data)
            
            if not messages:
                return {"error": f"No messages found in {file_path}"}
            
            # Smart extraction of key moments (LOCAL - no AI)
            key_moments = self.scanner.extract_key_moments(messages, metadata.title)
            
            if "error" in key_moments:
                return key_moments
            
            # Format for AI analysis (much smaller)
            formatted_conversation = self.scanner.format_for_ai_analysis(key_moments)
            
            # Estimate token savings
            original_tokens = sum(len(msg.content) for msg in messages) // 4
            optimized_tokens = self.scanner.estimate_tokens(formatted_conversation)
            
            print(f"   Token reduction: {original_tokens} → {optimized_tokens} ({((original_tokens - optimized_tokens) / original_tokens * 100):.1f}% saved)")
            
            # AI analysis of key moments only
            analysis = self.analyzer.analyze_conversation(formatted_conversation, metadata.title)
            
            if analysis:
                # Add file metadata
                metadata.file_path = file_path
                metadata.file_name = Path(file_path).name
                analysis.file_metadata = metadata
                
                # Add optimization metadata
                if not analysis.metadata:
                    analysis.metadata = {}
                analysis.metadata.update({
                    'optimization_stats': {
                        'original_tokens': original_tokens,
                        'optimized_tokens': optimized_tokens,
                        'token_savings_percent': round((original_tokens - optimized_tokens) / original_tokens * 100, 1),
                        'key_moments_extracted': len(key_moments.get('turning_points', [])),
                        'processing_method': 'smart_scanning'
                    }
                })
                
                print(f"   ✅ Analysis complete (Quality: {analysis.coaching_quality}/10)")
                return analysis
            else:
                return {"error": f"Failed to analyze {file_path}"}
                
        except Exception as e:
            error_msg = f"Exception processing {file_path}: {e}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}
    
    def process_batch_concurrent(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        Process a batch of files concurrently.
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            Dictionary mapping file paths to results
        """
        results = {}
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self.process_single_chat_optimized, file_path): file_path 
                for file_path in file_paths
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    with self.results_lock:
                        results[file_path] = result
                except Exception as e:
                    with self.results_lock:
                        results[file_path] = {"error": f"Future exception: {e}"}
        
        return results
    
    def process_all_conversations_optimized(self, days_back: int = config.DAYS_LOOKBACK) -> CoachingRecapResult:
        """
        Process all conversations with optimized batch processing.
        
        Args:
            days_back: Number of days to look back
            
        Returns:
            Complete analysis results
        """
        print(f"🚀 OPTIMIZED Coaching Recap Agent")
        print("=" * 50)
        print(f"🔍 Scanning for files modified in the last {days_back} days...")
        
        # Get recent files
        recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, days_back)
        
        if not recent_files:
            print(f"No files found modified in the last {days_back} days")
            return {"error": "No recent files found"}
        
        print(f"📁 Found {len(recent_files)} files to process")
        print(f"⚡ Using batch processing: {self.batch_size} files per batch, {self.max_workers} concurrent workers")
        
        # Process in batches
        all_results = {}
        successful_analyses = []
        failed_analyses = []
        
        total_batches = (len(recent_files) + self.batch_size - 1) // self.batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(recent_files))
            batch_files = recent_files[start_idx:end_idx]
            
            print(f"\n🔄 Processing batch {batch_num + 1}/{total_batches} ({len(batch_files)} files)")
            
            # Process batch concurrently
            batch_results = self.process_batch_concurrent(batch_files)
            
            # Merge results
            all_results.update(batch_results)
            
            # Track success/failure
            for file_path, result in batch_results.items():
                if isinstance(result, CoachingAnalysis):
                    successful_analyses.append(file_path)
                else:
                    failed_analyses.append(file_path)
            
            print(f"   Batch {batch_num + 1} complete: {len([r for r in batch_results.values() if isinstance(r, CoachingAnalysis)])} successful")
        
        # Create summary
        print(f"\n📊 Creating comprehensive summary...")
        summary = self.create_summary_optimized(all_results, successful_analyses, failed_analyses, days_back)
        
        # Create metadata
        metadata = RecapMetadata(
            processed_at=datetime.now().isoformat(),
            days_lookback=days_back,
            total_files_found=len(recent_files),
            successful_analyses=len(successful_analyses),
            failed_analyses=len(failed_analyses),
            model_used=config.OPENAI_MODEL
        )
        
        # Filter successful analyses
        individual_analyses = {
            path: analysis for path, analysis in all_results.items() 
            if isinstance(analysis, CoachingAnalysis)
        }
        
        # Create final output
        output = CoachingRecapResult(
            summary=summary,
            individual_analyses=individual_analyses,
            metadata=metadata
        )
        
        # Save results
        output_path = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME
        if save_json_file(output.dict(), str(output_path)):
            print(f"✅ Results saved to: {output_path}")
        
        # Print final stats
        print(f"\n🎉 OPTIMIZATION COMPLETE!")
        print(f"📈 Success rate: {len(successful_analyses)}/{len(recent_files)} ({len(successful_analyses)/len(recent_files)*100:.1f}%)")
        
        if successful_analyses:
            # Calculate token savings
            total_original_tokens = 0
            total_optimized_tokens = 0
            
            for analysis in individual_analyses.values():
                if analysis.metadata and 'optimization_stats' in analysis.metadata:
                    stats = analysis.metadata['optimization_stats']
                    total_original_tokens += stats.get('original_tokens', 0)
                    total_optimized_tokens += stats.get('optimized_tokens', 0)
            
            if total_original_tokens > 0:
                savings_percent = (total_original_tokens - total_optimized_tokens) / total_original_tokens * 100
                print(f"💰 Token savings: {total_original_tokens:,} → {total_optimized_tokens:,} ({savings_percent:.1f}% reduction)")
        
        return output
    
    def create_summary_optimized(self, results: Dict[str, Any], successful: List[str], failed: List[str], days_back: int) -> AnalysisSummary:
        """Create optimized summary with additional optimization stats."""
        from models import ProcessingStats, HighQualitySession, IntegrationOpportunity, ThemeFrequency
        
        # Extract successful analyses
        analyses = [results[path] for path in successful if isinstance(results[path], CoachingAnalysis)]
        
        if not analyses:
            return AnalysisSummary(
                period=f"Last {days_back} days",
                total_conversations=0,
                processing_stats=ProcessingStats(
                    successful=len(successful),
                    failed=len(failed),
                    success_rate="0%" if not successful else f"{len(successful)/(len(successful)+len(failed))*100:.1f}%"
                )
            )
        
        # Aggregate insights (same as before)
        arc_types = {}
        all_themes = []
        high_quality_sessions = []
        integration_opportunities = []
        
        for analysis in analyses:
            arc_type = analysis.arc_type
            arc_types[arc_type] = arc_types.get(arc_type, 0) + 1
            all_themes.extend(analysis.key_themes)
            
            if analysis.coaching_quality >= 8:
                title = analysis.file_metadata.title if analysis.file_metadata else 'Unknown'
                file_name = analysis.file_metadata.file_name if analysis.file_metadata else None
                high_quality_sessions.append(HighQualitySession(
                    title=title,
                    quality=analysis.coaching_quality,
                    arc_type=arc_type,
                    file_name=file_name
                ))
            
            if analysis.integration_potential:
                title = analysis.file_metadata.title if analysis.file_metadata else 'Unknown'
                file_name = analysis.file_metadata.file_name if analysis.file_metadata else None
                integration_opportunities.append(IntegrationOpportunity(
                    title=title,
                    opportunity=analysis.integration_potential,
                    file_name=file_name
                ))
        
        # Count theme frequency
        theme_counts = {}
        for theme in all_themes:
            theme_counts[theme] = theme_counts.get(theme, 0) + 1
        
        top_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        processing_stats = ProcessingStats(
            successful=len(successful),
            failed=len(failed),
            success_rate=f"{len(successful)/(len(successful)+len(failed))*100:.1f}%" if (successful or failed) else "0%"
        )
        
        return AnalysisSummary(
            period=f"Last {days_back} days (OPTIMIZED)",
            total_conversations=len(analyses),
            arc_type_distribution=arc_types,
            top_themes=[ThemeFrequency(theme=theme, frequency=count) for theme, count in top_themes],
            high_quality_sessions=high_quality_sessions,
            integration_opportunities=integration_opportunities,
            processing_stats=processing_stats
        )

def main():
    """Main function for optimized processing."""
    agent = OptimizedCoachingAgent(batch_size=10, max_workers=5)
    
    if not config.OPENAI_API_KEY:
        print("❌ Error: OPENAI_API_KEY not found in environment variables")
        return
    
    # Run optimized analysis
    results = agent.process_all_conversations_optimized(30)
    
    if hasattr(results, 'summary'):
        summary = results.summary
        print(f"\n📊 FINAL SUMMARY:")
        print(f"Period: {summary.period}")
        print(f"Total conversations: {summary.total_conversations}")
        print(f"Success rate: {summary.processing_stats.success_rate}")
        
        if summary.arc_type_distribution:
            print(f"\n🎭 Top Arc Types:")
            for arc_type, count in list(summary.arc_type_distribution.items())[:5]:
                print(f"  • {arc_type.replace('_', ' ').title()}: {count}")
        
        if summary.top_themes:
            print(f"\n🎯 Top Themes:")
            for theme in summary.top_themes[:5]:
                print(f"  • {theme.theme} ({theme.frequency}x)")

if __name__ == "__main__":
    main()
