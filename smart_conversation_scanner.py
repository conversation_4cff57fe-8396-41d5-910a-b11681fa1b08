"""
Smart Conversation Scanner - Extracts key coaching moments with minimal AI usage
"""
import re
from typing import List, Dict, Any, Tuple
from models import ChatMessage

class SmartConversationScanner:
    """Scans conversations locally to extract key coaching moments."""
    
    def __init__(self):
        """Initialize the scanner with coaching patterns."""
        
        # Patterns for detecting coaching moments
        self.patterns = {
            'start_signals': [
                r"I'm struggling with",
                r"I need help",
                r"The problem is",
                r"I'm stuck",
                r"I can't figure out",
                r"I'm confused about",
                r"I don't know how to",
                r"I'm having trouble",
                r"I'm overwhelmed",
                r"I feel lost"
            ],
            
            'insight_markers': [
                r"I realized",
                r"I see now",
                r"It clicked",
                r"The breakthrough",
                r"Now I understand",
                r"I get it",
                r"That makes sense",
                r"I never thought",
                r"The key insight",
                r"What I'm learning"
            ],
            
            'decision_points': [
                r"I've decided",
                r"I'm going to",
                r"The plan is",
                r"I will",
                r"My next step",
                r"I choose to",
                r"I commit to",
                r"Moving forward",
                r"I'll start by",
                r"The action is"
            ],
            
            'resistance_signs': [
                r"But I can't",
                r"I'm afraid",
                r"What if",
                r"I'm worried",
                r"I don't want to",
                r"I'm scared",
                r"That won't work",
                r"I've tried that",
                r"It's too hard",
                r"I'm not ready"
            ],
            
            'resolution_markers': [
                r"So the solution",
                r"I feel clear",
                r"I'm confident",
                r"This feels right",
                r"I know what to do",
                r"The path forward",
                r"I'm ready to",
                r"This makes sense",
                r"I feel good about",
                r"I'm excited to"
            ],
            
            'emotional_shifts': [
                r"I feel",
                r"I'm feeling",
                r"This brings up",
                r"I notice",
                r"There's a shift",
                r"Something changed",
                r"I'm experiencing",
                r"This feels",
                r"I sense",
                r"My body"
            ]
        }
        
        # Compile patterns for efficiency
        self.compiled_patterns = {}
        for category, patterns in self.patterns.items():
            self.compiled_patterns[category] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def extract_key_moments(self, messages: List[ChatMessage], title: str = "") -> Dict[str, Any]:
        """
        Extract key coaching moments from a conversation.
        
        Args:
            messages: List of ChatMessage objects
            title: Conversation title
            
        Returns:
            Dictionary with extracted key moments
        """
        if not messages:
            return {"error": "No messages to process"}
        
        # Convert messages to text segments
        segments = []
        for i, msg in enumerate(messages):
            segments.append({
                'index': i,
                'role': msg.role,
                'content': msg.content,
                'timestamp': msg.timestamp
            })
        
        # Extract key moments
        key_moments = {
            'opening': self._extract_opening(segments),
            'turning_points': self._extract_turning_points(segments),
            'resolution': self._extract_resolution(segments),
            'emotional_journey': self._extract_emotional_journey(segments),
            'metadata': {
                'title': title,
                'total_messages': len(messages),
                'user_messages': len([m for m in messages if m.role == 'user']),
                'assistant_messages': len([m for m in messages if m.role == 'assistant'])
            }
        }
        
        return key_moments
    
    def _extract_opening(self, segments: List[Dict]) -> Dict[str, Any]:
        """Extract the opening problem/question."""
        opening_segments = segments[:6]  # First 3 exchanges
        
        # Look for start signals in user messages
        start_content = []
        for segment in opening_segments:
            if segment['role'] == 'user':
                content = segment['content']
                
                # Check for start signal patterns
                for pattern in self.compiled_patterns['start_signals']:
                    if pattern.search(content):
                        start_content.append({
                            'content': content[:500],  # First 500 chars
                            'index': segment['index'],
                            'signal_type': 'start_signal'
                        })
                        break
                else:
                    # If no specific pattern, include first substantial user message
                    if len(content) > 50:
                        start_content.append({
                            'content': content[:500],
                            'index': segment['index'],
                            'signal_type': 'initial_message'
                        })
        
        return {
            'segments': start_content[:2],  # Max 2 opening segments
            'total_chars': sum(len(s['content']) for s in start_content[:2])
        }
    
    def _extract_turning_points(self, segments: List[Dict]) -> List[Dict[str, Any]]:
        """Extract key turning points and insights."""
        turning_points = []
        
        # Skip first and last few messages, focus on middle
        middle_segments = segments[6:-6] if len(segments) > 12 else segments[2:-2]
        
        for segment in middle_segments:
            content = segment['content']
            
            # Check for insight markers
            for pattern in self.compiled_patterns['insight_markers']:
                if pattern.search(content):
                    turning_points.append({
                        'content': content[:800],  # More context for insights
                        'index': segment['index'],
                        'type': 'insight',
                        'role': segment['role']
                    })
                    break
            
            # Check for decision points
            for pattern in self.compiled_patterns['decision_points']:
                if pattern.search(content):
                    turning_points.append({
                        'content': content[:600],
                        'index': segment['index'],
                        'type': 'decision',
                        'role': segment['role']
                    })
                    break
            
            # Check for resistance signs (important for coaching)
            for pattern in self.compiled_patterns['resistance_signs']:
                if pattern.search(content):
                    turning_points.append({
                        'content': content[:600],
                        'index': segment['index'],
                        'type': 'resistance',
                        'role': segment['role']
                    })
                    break
        
        # Sort by index and limit to top 5 turning points
        turning_points.sort(key=lambda x: x['index'])
        return turning_points[:5]
    
    def _extract_resolution(self, segments: List[Dict]) -> Dict[str, Any]:
        """Extract the resolution/conclusion."""
        resolution_segments = segments[-6:]  # Last 3 exchanges
        
        resolution_content = []
        for segment in resolution_segments:
            content = segment['content']
            
            # Check for resolution markers
            for pattern in self.compiled_patterns['resolution_markers']:
                if pattern.search(content):
                    resolution_content.append({
                        'content': content[:600],
                        'index': segment['index'],
                        'type': 'resolution',
                        'role': segment['role']
                    })
                    break
            else:
                # Include substantial final messages
                if len(content) > 100 and segment['role'] == 'user':
                    resolution_content.append({
                        'content': content[:600],
                        'index': segment['index'],
                        'type': 'final_message',
                        'role': segment['role']
                    })
        
        return {
            'segments': resolution_content[-2:],  # Last 2 resolution segments
            'total_chars': sum(len(s['content']) for s in resolution_content[-2:])
        }
    
    def _extract_emotional_journey(self, segments: List[Dict]) -> List[Dict[str, Any]]:
        """Extract emotional shifts and body awareness moments."""
        emotional_moments = []
        
        for segment in segments:
            if segment['role'] == 'user':  # Focus on user's emotional experience
                content = segment['content']
                
                for pattern in self.compiled_patterns['emotional_shifts']:
                    if pattern.search(content):
                        emotional_moments.append({
                            'content': content[:400],
                            'index': segment['index'],
                            'type': 'emotional_shift'
                        })
                        break
        
        # Limit to 3 most significant emotional moments
        return emotional_moments[:3]
    
    def format_for_ai_analysis(self, key_moments: Dict[str, Any]) -> str:
        """
        Format extracted key moments into a concise prompt for AI analysis.
        
        Args:
            key_moments: Extracted key moments
            
        Returns:
            Formatted string for AI analysis (~1000 tokens vs 12000)
        """
        formatted = []
        
        # Add title
        title = key_moments.get('metadata', {}).get('title', 'Untitled')
        formatted.append(f"CONVERSATION: {title}")
        formatted.append("=" * 50)
        
        # Add opening
        opening = key_moments.get('opening', {})
        if opening.get('segments'):
            formatted.append("\n🎯 OPENING PROBLEM/QUESTION:")
            for segment in opening['segments']:
                formatted.append(f"• {segment['content']}")
        
        # Add turning points
        turning_points = key_moments.get('turning_points', [])
        if turning_points:
            formatted.append(f"\n🔄 KEY TURNING POINTS ({len(turning_points)} moments):")
            for i, tp in enumerate(turning_points, 1):
                formatted.append(f"{i}. [{tp['type'].upper()}] {tp['content']}")
        
        # Add resolution
        resolution = key_moments.get('resolution', {})
        if resolution.get('segments'):
            formatted.append("\n✅ RESOLUTION/CONCLUSION:")
            for segment in resolution['segments']:
                formatted.append(f"• {segment['content']}")
        
        # Add emotional journey
        emotional = key_moments.get('emotional_journey', [])
        if emotional:
            formatted.append(f"\n💫 EMOTIONAL SHIFTS ({len(emotional)} moments):")
            for moment in emotional:
                formatted.append(f"• {moment['content']}")
        
        # Add metadata
        metadata = key_moments.get('metadata', {})
        formatted.append(f"\n📊 CONVERSATION STATS:")
        formatted.append(f"Total messages: {metadata.get('total_messages', 0)}")
        formatted.append(f"User messages: {metadata.get('user_messages', 0)}")
        
        return "\n".join(formatted)
    
    def estimate_tokens(self, text: str) -> int:
        """Rough token estimation (4 chars per token)."""
        return len(text) // 4
