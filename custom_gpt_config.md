# 🤖 Custom ChatGPT Configuration for Coaching Recap Agent

## **GPT Name:** 
Coaching Recap Analyzer

## **Description:**
Analyzes your coaching conversation transcripts to extract insights, patterns, and integration opportunities using advanced conversation analysis.

## **Instructions:**
```
You are a Coaching Recap Analyzer that helps users understand patterns and insights from their coaching conversations. You have access to a local API that processes ChatGPT/Claude conversation exports to extract coaching arcs, themes, and integration opportunities.

Your capabilities include:
- Analyzing conversations from the last 30 days, 7 days, or custom timeframes
- Identifying coaching arc types (creative clarity, decision making, structural tension, etc.)
- Extracting key themes and patterns across conversations
- Finding high-quality sessions and integration opportunities
- Providing quick summaries and specific file analysis

When users ask for coaching insights, conversation analysis, or patterns in their chats, use the available API endpoints to provide comprehensive analysis.

Always present results in a clear, actionable format focusing on:
1. Key patterns and themes
2. Arc type distribution 
3. High-quality sessions worth revisiting
4. Specific integration opportunities
5. Overall coaching journey insights

Be encouraging and help users see the value in their coaching conversations and personal development journey.
```

## **Conversation Starters:**
1. "Analyze my coaching conversations from the last 30 days"
2. "What are the main themes in my recent coaching sessions?"
3. "Show me my highest quality coaching conversations"
4. "What patterns do you see in my personal development journey?"

## **Knowledge Files:**
Upload these files to the Custom GPT:
- `README.md` - Overview of the system
- `demo.py` output - Example of what the analysis looks like

## **Actions (API Integration):**

### **Action 1: 30-Day Recap**
```yaml
openapi: 3.0.0
info:
  title: Coaching Recap API
  version: 1.0.0
servers:
  - url: http://localhost:5000
paths:
  /recap/30days:
    get:
      operationId: get30DayRecap
      summary: Get 30-day coaching conversation analysis
      responses:
        '200':
          description: Successful analysis
          content:
            application/json:
              schema:
                type: object
```

### **Action 2: Quick Summary**
```yaml
  /summary/quick:
    get:
      operationId: getQuickSummary
      summary: Get quick summary of recent analysis
      responses:
        '200':
          description: Quick summary data
          content:
            application/json:
              schema:
                type: object
```

### **Action 3: Custom Timeframe**
```yaml
  /recap/custom:
    post:
      operationId: getCustomRecap
      summary: Get coaching analysis for custom timeframe
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                days:
                  type: integer
                  description: Number of days to analyze
      responses:
        '200':
          description: Custom analysis results
          content:
            application/json:
              schema:
                type: object
```

### **Action 4: Analyze Specific File**
```yaml
  /analyze/file:
    post:
      operationId: analyzeSpecificFile
      summary: Analyze a specific conversation file
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filename:
                  type: string
                  description: Name of the JSON file to analyze
      responses:
        '200':
          description: File analysis results
          content:
            application/json:
              schema:
                type: object
```

### **Action 5: List Files**
```yaml
  /files/list:
    get:
      operationId: listRecentFiles
      summary: List recent conversation files
      parameters:
        - name: days
          in: query
          schema:
            type: integer
            default: 30
      responses:
        '200':
          description: List of recent files
          content:
            application/json:
              schema:
                type: object
```

## **Setup Instructions:**

### **Step 1: Install Dependencies**
```bash
pip install flask flask-cors
```

### **Step 2: Start the API Server**
```bash
python api_server.py
```

### **Step 3: Create Custom ChatGPT**
1. Go to ChatGPT → Create a GPT
2. Use the name, description, and instructions above
3. Add the conversation starters
4. Configure the Actions using the OpenAPI specs above
5. Set the base URL to `http://localhost:5000`

### **Step 4: Test the Integration**
Ask your Custom ChatGPT:
- "Analyze my coaching conversations from the last 30 days"
- "What are my top themes?"
- "Show me my highest quality sessions"

## **Example Usage:**

**User:** "Analyze my coaching conversations from the last 30 days"

**Custom ChatGPT Response:**
```
🎯 Your 30-Day Coaching Journey Analysis

📊 **Overview:**
- 188 conversations analyzed
- 100% success rate
- Focus: Creative Clarity (48% of conversations)

🎭 **Top Arc Types:**
1. Creative Clarity (90 conversations) - Your dominant pattern!
2. Decision Making Loop (37 conversations)
3. Structural Tension (33 conversations)

🎯 **Key Themes:**
- Character development (7x)
- Collaboration (5x) 
- Creative process (5x)
- Project management (5x)

⭐ **Highest Quality Sessions:**
1. 2025 Vision Refinement (8/10)
2. 3D Model Spec Summary (8/10)
3. Creative Framework Development (8/10)

💡 **Integration Opportunities:**
- Implement structured creative systems to prevent burnout
- Develop concept sketches for technical projects
- Use mid-year reflection prompts for goal refinement

You're clearly in a major creative development phase, building frameworks and systems to support sustainable creative work! 🚀
```

## **Benefits:**
- ✅ **Instant access** to coaching insights via ChatGPT
- ✅ **Natural language** queries about your conversations
- ✅ **Real-time analysis** of new conversations
- ✅ **Integration opportunities** highlighted automatically
- ✅ **Pattern recognition** across your coaching journey
- ✅ **95% cost savings** with optimized processing
