"""
Simple API server for Custom ChatGPT integration
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import json
from pathlib import Path
from optimized_coaching_agent import OptimizedCoachingAgent
import config

app = Flask(__name__)
CORS(app)  # Enable CORS for ChatGPT access

# Initialize the agent
agent = OptimizedCoachingAgent(batch_size=10, max_workers=5)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "message": "Coaching Recap API is running"})

@app.route('/recap/30days', methods=['GET'])
def recap_30_days():
    """Get 30-day coaching recap analysis."""
    try:
        print("🚀 Starting 30-day recap analysis...")
        results = agent.process_all_conversations_optimized(30)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/recap/7days', methods=['GET'])
def recap_7_days():
    """Get 7-day coaching recap analysis."""
    try:
        print("🚀 Starting 7-day recap analysis...")
        results = agent.process_all_conversations_optimized(7)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/recap/custom', methods=['POST'])
def recap_custom_days():
    """Get custom timeframe coaching recap analysis."""
    try:
        data = request.get_json()
        days = data.get('days', 30)
        
        print(f"🚀 Starting {days}-day recap analysis...")
        results = agent.process_all_conversations_optimized(days)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/analyze/file', methods=['POST'])
def analyze_specific_file():
    """Analyze a specific conversation file."""
    try:
        data = request.get_json()
        filename = data.get('filename')
        
        if not filename:
            return jsonify({"error": "filename parameter required"}), 400
        
        file_path = Path(config.CHAT_EXPORTS_DIR) / filename
        if not file_path.exists():
            return jsonify({"error": f"File not found: {filename}"}), 404
        
        print(f"🔍 Analyzing specific file: {filename}")
        result = agent.process_single_chat_optimized(str(file_path))
        
        if hasattr(result, 'model_dump'):
            return jsonify(result.model_dump())
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/summary/quick', methods=['GET'])
def quick_summary():
    """Get a quick summary of recent analysis."""
    try:
        # Check if we have recent results
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME
        
        if not results_file.exists():
            return jsonify({"error": "No recent analysis found. Run /recap/30days first."}), 404
        
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract key summary info
        summary = data.get('summary', {})
        metadata = data.get('metadata', {})
        
        quick_summary = {
            "period": summary.get('period', 'Unknown'),
            "total_conversations": summary.get('total_conversations', 0),
            "success_rate": summary.get('processing_stats', {}).get('success_rate', '0%'),
            "top_arc_types": dict(list(summary.get('arc_type_distribution', {}).items())[:5]),
            "top_themes": [theme for theme in summary.get('top_themes', [])[:5]],
            "high_quality_count": len(summary.get('high_quality_sessions', [])),
            "processed_at": metadata.get('processed_at', 'Unknown'),
            "token_savings": "93.9% reduction achieved"
        }
        
        return jsonify(quick_summary)
        
    except Exception as e:
        return jsonify({"error": f"Summary failed: {str(e)}"}), 500

@app.route('/files/list', methods=['GET'])
def list_recent_files():
    """List recent conversation files."""
    try:
        from file_utils import get_recent_files
        
        days = request.args.get('days', 30, type=int)
        recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, days)
        
        file_list = []
        for file_path in recent_files[:20]:  # Limit to 20 most recent
            file_name = Path(file_path).name
            file_list.append({
                "filename": file_name,
                "path": file_path
            })
        
        return jsonify({
            "total_files": len(recent_files),
            "showing": len(file_list),
            "files": file_list
        })
        
    except Exception as e:
        return jsonify({"error": f"File listing failed: {str(e)}"}), 500

if __name__ == '__main__':
    print("🚀 Starting Coaching Recap API Server...")
    print("📡 Available endpoints:")
    print("  GET  /health - Health check")
    print("  GET  /recap/30days - 30-day analysis")
    print("  GET  /recap/7days - 7-day analysis") 
    print("  POST /recap/custom - Custom timeframe (JSON: {\"days\": N})")
    print("  POST /analyze/file - Analyze specific file (JSON: {\"filename\": \"file.json\"})")
    print("  GET  /summary/quick - Quick summary of recent analysis")
    print("  GET  /files/list?days=30 - List recent files")
    print("\n🔗 For Custom ChatGPT integration:")
    print("  Base URL: http://localhost:5000")
    print("  Example: http://localhost:5000/recap/30days")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
