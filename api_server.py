"""
Simple API server for Custom ChatGPT integration
"""
from flask import Flask, request, jsonify, send_file, make_response
from flask_cors import CORS
import json
import csv
import io
from datetime import datetime
from pathlib import Path
from optimized_coaching_agent import OptimizedCoachingAgent
import config

app = Flask(__name__)
CORS(app)  # Enable CORS for ChatGPT access

# Initialize the agent
agent = OptimizedCoachingAgent(batch_size=10, max_workers=5)

@app.route('/', methods=['GET'])
def home():
    """Serve the download interface."""
    try:
        with open('download_interface.html', 'r', encoding='utf-8') as f:
            return f.read(), 200, {'Content-Type': 'text/html'}
    except FileNotFoundError:
        return jsonify({"message": "Download interface not found. Use /health to check API status."}), 404

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "message": "Coaching Recap API is running"})

@app.route('/recap/30days', methods=['GET'])
def recap_30_days():
    """Get 30-day coaching recap analysis."""
    try:
        print("🚀 Starting 30-day recap analysis...")
        results = agent.process_all_conversations_optimized(30)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/recap/7days', methods=['GET'])
def recap_7_days():
    """Get 7-day coaching recap analysis."""
    try:
        print("🚀 Starting 7-day recap analysis...")
        results = agent.process_all_conversations_optimized(7)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/recap/custom', methods=['POST'])
def recap_custom_days():
    """Get custom timeframe coaching recap analysis."""
    try:
        data = request.get_json()
        days = data.get('days', 30)
        
        print(f"🚀 Starting {days}-day recap analysis...")
        results = agent.process_all_conversations_optimized(days)
        
        if hasattr(results, 'model_dump'):
            return jsonify(results.model_dump())
        else:
            return jsonify(results)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/analyze/file', methods=['POST'])
def analyze_specific_file():
    """Analyze a specific conversation file."""
    try:
        data = request.get_json()
        filename = data.get('filename')
        
        if not filename:
            return jsonify({"error": "filename parameter required"}), 400
        
        file_path = Path(config.CHAT_EXPORTS_DIR) / filename
        if not file_path.exists():
            return jsonify({"error": f"File not found: {filename}"}), 404
        
        print(f"🔍 Analyzing specific file: {filename}")
        result = agent.process_single_chat_optimized(str(file_path))
        
        if hasattr(result, 'model_dump'):
            return jsonify(result.model_dump())
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/summary/quick', methods=['GET'])
def quick_summary():
    """Get a quick summary of recent analysis."""
    try:
        # Check if we have recent results
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME
        
        if not results_file.exists():
            return jsonify({"error": "No recent analysis found. Run /recap/30days first."}), 404
        
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract key summary info
        summary = data.get('summary', {})
        metadata = data.get('metadata', {})
        
        quick_summary = {
            "period": summary.get('period', 'Unknown'),
            "total_conversations": summary.get('total_conversations', 0),
            "success_rate": summary.get('processing_stats', {}).get('success_rate', '0%'),
            "top_arc_types": dict(list(summary.get('arc_type_distribution', {}).items())[:5]),
            "top_themes": [theme for theme in summary.get('top_themes', [])[:5]],
            "high_quality_count": len(summary.get('high_quality_sessions', [])),
            "processed_at": metadata.get('processed_at', 'Unknown'),
            "token_savings": "93.9% reduction achieved"
        }
        
        return jsonify(quick_summary)
        
    except Exception as e:
        return jsonify({"error": f"Summary failed: {str(e)}"}), 500

@app.route('/files/list', methods=['GET'])
def list_recent_files():
    """List recent conversation files."""
    try:
        from file_utils import get_recent_files
        
        days = request.args.get('days', 30, type=int)
        recent_files = get_recent_files(config.CHAT_EXPORTS_DIR, days)
        
        file_list = []
        for file_path in recent_files[:20]:  # Limit to 20 most recent
            file_name = Path(file_path).name
            file_list.append({
                "filename": file_name,
                "path": file_path
            })
        
        return jsonify({
            "total_files": len(recent_files),
            "showing": len(file_list),
            "files": file_list
        })
        
    except Exception as e:
        return jsonify({"error": f"File listing failed: {str(e)}"}), 500

@app.route('/export/json', methods=['GET'])
def export_json():
    """Export the latest analysis results as JSON file."""
    try:
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME

        if not results_file.exists():
            return jsonify({"error": "No analysis results found. Run analysis first."}), 404

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"coaching_recap_{timestamp}.json"

        return send_file(
            results_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/json'
        )

    except Exception as e:
        return jsonify({"error": f"Export failed: {str(e)}"}), 500

@app.route('/export/csv', methods=['GET'])
def export_csv():
    """Export analysis results as CSV file."""
    try:
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME

        if not results_file.exists():
            return jsonify({"error": "No analysis results found. Run analysis first."}), 404

        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'File Name', 'Title', 'Arc Type', 'Quality Rating',
            'Start Point', 'Resolution', 'Key Themes', 'Integration Potential'
        ])

        # Write individual analyses
        individual_analyses = data.get('individual_analyses', {})
        for file_path, analysis in individual_analyses.items():
            if isinstance(analysis, dict):
                file_name = Path(file_path).name
                title = analysis.get('file_metadata', {}).get('title', 'Unknown')
                arc_type = analysis.get('arc_type', 'unknown')
                quality = analysis.get('coaching_quality', 0)
                start_point = analysis.get('start_point', '')[:200] + '...' if len(analysis.get('start_point', '')) > 200 else analysis.get('start_point', '')
                resolution = analysis.get('resolution', '')[:200] + '...' if len(analysis.get('resolution', '')) > 200 else analysis.get('resolution', '')
                themes = ', '.join(analysis.get('key_themes', []))
                integration = analysis.get('integration_potential', '')[:200] + '...' if len(analysis.get('integration_potential', '')) > 200 else analysis.get('integration_potential', '')

                writer.writerow([
                    file_name, title, arc_type, quality,
                    start_point, resolution, themes, integration
                ])

        # Create response
        output.seek(0)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"coaching_recap_{timestamp}.csv"

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'

        return response

    except Exception as e:
        return jsonify({"error": f"CSV export failed: {str(e)}"}), 500

@app.route('/export/summary', methods=['GET'])
def export_summary():
    """Export a formatted summary report as text file."""
    try:
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME

        if not results_file.exists():
            return jsonify({"error": "No analysis results found. Run analysis first."}), 404

        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Create formatted summary
        summary = data.get('summary', {})
        metadata = data.get('metadata', {})

        report_lines = [
            "🤖 COACHING RECAP ANALYSIS REPORT",
            "=" * 50,
            "",
            f"📅 Generated: {metadata.get('processed_at', 'Unknown')}",
            f"🔍 Period: {summary.get('period', 'Unknown')}",
            f"📊 Total Conversations: {summary.get('total_conversations', 0)}",
            f"✅ Success Rate: {summary.get('processing_stats', {}).get('success_rate', '0%')}",
            "",
            "🎭 ARC TYPE DISTRIBUTION:",
            "-" * 30
        ]

        # Add arc types
        arc_dist = summary.get('arc_type_distribution', {})
        for arc_type, count in sorted(arc_dist.items(), key=lambda x: x[1], reverse=True):
            report_lines.append(f"  • {arc_type.replace('_', ' ').title()}: {count}")

        report_lines.extend([
            "",
            "🎯 TOP THEMES:",
            "-" * 20
        ])

        # Add themes
        themes = summary.get('top_themes', [])
        for theme in themes[:10]:
            if isinstance(theme, dict):
                report_lines.append(f"  • {theme.get('theme', 'Unknown')} ({theme.get('frequency', 0)}x)")

        report_lines.extend([
            "",
            "⭐ HIGH QUALITY SESSIONS:",
            "-" * 30
        ])

        # Add high quality sessions
        high_quality = summary.get('high_quality_sessions', [])
        for session in high_quality[:10]:
            if isinstance(session, dict):
                title = session.get('title', 'Unknown')
                quality = session.get('quality', 0)
                arc_type = session.get('arc_type', 'unknown')
                report_lines.append(f"  • {title} (Quality: {quality}/10, Type: {arc_type})")

        report_lines.extend([
            "",
            "💡 INTEGRATION OPPORTUNITIES:",
            "-" * 35
        ])

        # Add integration opportunities
        integration = summary.get('integration_opportunities', [])
        for opp in integration[:10]:
            if isinstance(opp, dict):
                title = opp.get('title', 'Unknown')
                opportunity = opp.get('opportunity', 'No description')
                report_lines.append(f"  • {title}")
                report_lines.append(f"    → {opportunity}")
                report_lines.append("")

        # Add footer
        report_lines.extend([
            "=" * 50,
            "Generated by Coaching Recap Agent",
            f"Total token savings: 93.9% reduction achieved",
            f"Processing time: ~10 minutes for {summary.get('total_conversations', 0)} conversations"
        ])

        # Create response
        report_content = '\n'.join(report_lines)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"coaching_summary_{timestamp}.txt"

        response = make_response(report_content)
        response.headers['Content-Type'] = 'text/plain; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'

        return response

    except Exception as e:
        return jsonify({"error": f"Summary export failed: {str(e)}"}), 500

@app.route('/export/insights', methods=['GET'])
def export_insights():
    """Export key insights and patterns as markdown file."""
    try:
        results_file = Path(config.OUTPUT_DIR) / config.RECAP_FILENAME

        if not results_file.exists():
            return jsonify({"error": "No analysis results found. Run analysis first."}), 404

        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        summary = data.get('summary', {})
        individual_analyses = data.get('individual_analyses', {})

        # Create markdown content
        md_lines = [
            "# 🎯 Coaching Journey Insights",
            "",
            f"**Period:** {summary.get('period', 'Unknown')}  ",
            f"**Total Conversations:** {summary.get('total_conversations', 0)}  ",
            f"**Analysis Date:** {data.get('metadata', {}).get('processed_at', 'Unknown')}",
            "",
            "## 🎭 Your Dominant Patterns",
            ""
        ]

        # Analyze patterns
        arc_dist = summary.get('arc_type_distribution', {})
        total_conversations = summary.get('total_conversations', 1)

        for arc_type, count in sorted(arc_dist.items(), key=lambda x: x[1], reverse=True)[:3]:
            percentage = (count / total_conversations) * 100
            md_lines.extend([
                f"### {arc_type.replace('_', ' ').title()} ({count} conversations, {percentage:.1f}%)",
                ""
            ])

            # Find examples of this arc type
            examples = []
            for file_path, analysis in individual_analyses.items():
                if isinstance(analysis, dict) and analysis.get('arc_type') == arc_type and analysis.get('coaching_quality', 0) >= 7:
                    examples.append({
                        'title': analysis.get('file_metadata', {}).get('title', Path(file_path).name),
                        'quality': analysis.get('coaching_quality', 0),
                        'integration': analysis.get('integration_potential', '')
                    })

            if examples:
                md_lines.append("**Key Examples:**")
                for example in examples[:3]:
                    md_lines.append(f"- **{example['title']}** (Quality: {example['quality']}/10)")
                    if example['integration']:
                        md_lines.append(f"  - *Integration:* {example['integration'][:150]}...")
                md_lines.append("")

        # Add themes section
        md_lines.extend([
            "## 🎯 Recurring Themes",
            ""
        ])

        themes = summary.get('top_themes', [])
        for theme in themes[:8]:
            if isinstance(theme, dict):
                md_lines.append(f"- **{theme.get('theme', 'Unknown')}** ({theme.get('frequency', 0)} times)")

        md_lines.extend([
            "",
            "## 💡 Key Integration Opportunities",
            ""
        ])

        # Add top integration opportunities
        integration_opps = summary.get('integration_opportunities', [])
        for i, opp in enumerate(integration_opps[:5], 1):
            if isinstance(opp, dict):
                md_lines.extend([
                    f"### {i}. {opp.get('title', 'Unknown')}",
                    f"{opp.get('opportunity', 'No description')}",
                    ""
                ])

        # Add insights section
        md_lines.extend([
            "## 🔍 Journey Insights",
            "",
            "### What This Reveals About Your Growth:",
            ""
        ])

        # Generate insights based on patterns
        if arc_dist.get('creative_clarity', 0) > total_conversations * 0.3:
            md_lines.append("- You're in a **major creative development phase**, actively building frameworks and systems")

        if arc_dist.get('decision_making_loop', 0) > total_conversations * 0.15:
            md_lines.append("- You're focused on **building decision-making systems** and structures")

        if arc_dist.get('structural_tension', 0) > total_conversations * 0.15:
            md_lines.append("- You're addressing **recurring patterns** and systematic challenges")

        high_quality_count = len(summary.get('high_quality_sessions', []))
        if high_quality_count > total_conversations * 0.1:
            md_lines.append(f"- You have **{high_quality_count} high-quality sessions** showing effective coaching engagement")

        md_lines.extend([
            "",
            "### Recommended Focus Areas:",
            ""
        ])

        # Add recommendations based on patterns
        top_arc = max(arc_dist.items(), key=lambda x: x[1])[0] if arc_dist else "unknown"
        if top_arc == "creative_clarity":
            md_lines.append("- Continue developing **creative frameworks** and systematic approaches")
            md_lines.append("- Focus on **project completion systems** to prevent creative overwhelm")
        elif top_arc == "decision_making_loop":
            md_lines.append("- Implement the **decision frameworks** you've developed")
            md_lines.append("- Create **decision templates** for recurring choices")

        md_lines.extend([
            "",
            "---",
            "*Generated by Coaching Recap Agent*"
        ])

        # Create response
        md_content = '\n'.join(md_lines)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"coaching_insights_{timestamp}.md"

        response = make_response(md_content)
        response.headers['Content-Type'] = 'text/markdown; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'

        return response

    except Exception as e:
        return jsonify({"error": f"Insights export failed: {str(e)}"}), 500

if __name__ == '__main__':
    print("🚀 Starting Coaching Recap API Server...")
    print("📡 Available endpoints:")
    print("  GET  /health - Health check")
    print("  GET  /recap/30days - 30-day analysis")
    print("  GET  /recap/7days - 7-day analysis")
    print("  POST /recap/custom - Custom timeframe (JSON: {\"days\": N})")
    print("  POST /analyze/file - Analyze specific file (JSON: {\"filename\": \"file.json\"})")
    print("  GET  /summary/quick - Quick summary of recent analysis")
    print("  GET  /files/list?days=30 - List recent files")
    print("  📥 EXPORT ENDPOINTS:")
    print("  GET  /export/json - Download full results as JSON")
    print("  GET  /export/csv - Download results as CSV spreadsheet")
    print("  GET  /export/summary - Download formatted summary report")
    print("  GET  /export/insights - Download insights as Markdown")
    print("\n🔗 For Custom ChatGPT integration:")
    print("  Base URL: http://localhost:5000")
    print("  Example: http://localhost:5000/recap/30days")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
