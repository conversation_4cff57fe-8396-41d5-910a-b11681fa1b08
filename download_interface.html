<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coaching Recap Downloads</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 40px;
            font-size: 1.2em;
        }
        
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .download-card {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .download-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .download-card h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .download-card p {
            color: #718096;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }
        
        .status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }
        
        .status.loading {
            background: #ebf8ff;
            color: #3182ce;
            border: 1px solid #90cdf4;
        }
        
        .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .footer {
            text-align: center;
            color: #718096;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Coaching Recap Downloads</h1>
        <p class="subtitle">Export your coaching conversation analysis in multiple formats</p>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="download-grid">
            <div class="download-card" onclick="downloadFile('json')">
                <div class="icon">📊</div>
                <h3>Complete JSON Data</h3>
                <p>Full analysis results with all details, metadata, and individual conversation breakdowns. Perfect for developers or detailed analysis.</p>
                <button class="download-btn">Download JSON</button>
            </div>
            
            <div class="download-card" onclick="downloadFile('csv')">
                <div class="icon">📈</div>
                <h3>CSV Spreadsheet</h3>
                <p>Structured data in spreadsheet format. Great for Excel analysis, filtering, and creating custom charts of your coaching patterns.</p>
                <button class="download-btn">Download CSV</button>
            </div>
            
            <div class="download-card" onclick="downloadFile('summary')">
                <div class="icon">📋</div>
                <h3>Summary Report</h3>
                <p>Clean, formatted text report with key insights, arc types, themes, and integration opportunities. Easy to read and share.</p>
                <button class="download-btn">Download Report</button>
            </div>
            
            <div class="download-card" onclick="downloadFile('insights')">
                <div class="icon">💡</div>
                <h3>Insights & Patterns</h3>
                <p>Markdown file with deep insights about your coaching journey, growth patterns, and personalized recommendations.</p>
                <button class="download-btn">Download Insights</button>
            </div>
        </div>
        
        <div class="footer">
            <p>🔒 All data is processed locally and securely</p>
            <p>Generated by Coaching Recap Agent • 93.9% token optimization achieved</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }
        
        async function downloadFile(format) {
            showStatus('Preparing download...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/export/${format}`);
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Download failed');
                }
                
                // Get filename from response headers
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `coaching_recap.${format}`;
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename=(.+)/);
                    if (filenameMatch) {
                        filename = filenameMatch[1].replace(/"/g, '');
                    }
                }
                
                // Create blob and download
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showStatus(`✅ ${filename} downloaded successfully!`, 'success');
                
            } catch (error) {
                console.error('Download error:', error);
                showStatus(`❌ Download failed: ${error.message}`, 'error');
            }
        }
        
        // Check API health on load
        async function checkAPIHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    showStatus('✅ API connected - Ready to download!', 'success');
                } else {
                    showStatus('⚠️ API not responding - Make sure the server is running', 'error');
                }
            } catch (error) {
                showStatus('❌ Cannot connect to API - Start the server with: python start_api.py', 'error');
            }
        }
        
        // Check health on page load
        window.addEventListener('load', checkAPIHealth);
    </script>
</body>
</html>
