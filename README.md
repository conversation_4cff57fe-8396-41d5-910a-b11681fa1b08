# 🤖 Coaching Recap Agent

A local Python-based agent that processes saved ChatGPT/Claude conversation transcripts to extract **coaching arcs** and insights. Built with Pydantic for type safety and OpenAI GPT-4o for intelligent analysis.

## 🎯 Purpose

This agent analyzes your coaching conversations to identify:

- **Starting point** (initial goal/confusion)
- **Key shifts/decisions** (turning points)
- **Final resolution/insight** (outcomes)
- **Open/unresolved threads** (what's still pending)
- **Overall arc type** (emotional breakthrough, creative clarity, etc.)

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Environment

Create a `.env` file in the project root:

```env
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Add Your Chat Exports

Place your ChatGPT/Claude JSON export files in the `ChatExports/` directory.

### 4. Run the Agent

```bash
python coaching_recap_agent.py
```

## 📁 Project Structure

```
StructuralSage/
├── ChatExports/           # Your JSON chat files go here
├── output/               # Generated analysis results
├── coaching_recap_agent.py  # Main agent script
├── models.py            # Pydantic data models
├── arc_analyzer.py      # GPT analysis logic
├── chat_processor.py    # Message extraction & formatting
├── file_utils.py        # File handling utilities
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## 🔧 Configuration

Edit `config.py` to customize:

- **Days lookback**: How far back to analyze (default: 30 days)
- **Token limits**: Max tokens per conversation
- **Arc types**: Categories for coaching conversations
- **File paths**: Input/output directories

## 📊 Output Format

The agent generates structured analysis including:

### Individual Conversation Analysis
- Start point and resolution
- Turning points with insights
- Arc type classification
- Key themes
- Coaching quality rating (1-10)
- Integration opportunities

### Summary Report
- Arc type distribution
- Most frequent themes
- High-quality sessions
- Processing statistics

## 🎭 Arc Types

The agent classifies conversations into these coaching arc types:

- **emotional_breakthrough**: Processing emotions, healing, releasing blocks
- **creative_clarity**: Gaining clarity on creative projects, vision, or expression
- **decision_making_loop**: Working through decisions, choices, or next steps
- **structural_tension**: Addressing systemic patterns, structures, or recurring issues
- **identity_integration**: Exploring identity, roles, or self-concept
- **resistance_breakdown**: Working through resistance, procrastination, or avoidance
- **visionary_expansion**: Expanding vision, possibilities, or future thinking
- **collaboration_wound**: Processing relationship, team, or collaboration challenges
- **sovereignty_reclaim**: Reclaiming personal power, boundaries, or autonomy

## 🔌 Augment Integration

When the Augment SDK is available, the agent registers these actions:

- `recap_last_30_days()` - Analyze conversations from last 30 days
- `recap_last_7_days()` - Analyze conversations from last 7 days  
- `analyze_specific_chat(file_name)` - Analyze a specific chat file

## 🛠️ Usage Examples

### Standalone Mode
```bash
python coaching_recap_agent.py
```

### Programmatic Usage
```python
from coaching_recap_agent import CoachingRecapAgent

agent = CoachingRecapAgent()

# Analyze last 30 days
results = agent.process_recent_chats(30)

# Analyze specific file
analysis = agent.process_single_chat("ChatExports/Creative Blocks & Solutions.json")
```

### With Augment (when available)
```python
# These functions are automatically registered as Augment actions
recap_last_30_days()
recap_last_7_days()
analyze_specific_chat("Creative Blocks & Solutions.json")
```

## 📋 Requirements

- Python 3.8+
- OpenAI API key
- JSON chat export files from ChatGPT or Claude

## 🔍 Supported Chat Formats

The agent currently supports JSON exports from:
- ChatGPT (OpenAI format)
- Claude (Anthropic format)

Expected JSON structure:
```json
{
  "title": "Conversation Title",
  "created": 1234567890,
  "updated": 1234567890,
  "messages": [
    {
      "role": "user",
      "content": "Message content...",
      "timestamp": 1234567890
    }
  ]
}
```

## 🚨 Error Handling

The agent includes robust error handling for:
- Invalid JSON files
- Missing API keys
- Token limit exceeded
- Network issues
- Malformed responses

## 📈 Performance

- Processes ~10-20 conversations per minute
- Automatically truncates long conversations to stay within token limits
- Saves results locally for offline review
- Provides detailed processing statistics

## 🤝 Contributing

This is a personal coaching tool, but feel free to adapt it for your own use cases!

## 📄 License

Private use only.
