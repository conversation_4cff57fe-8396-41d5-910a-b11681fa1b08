"""
Startup script for the Coaching Recap API
"""
import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required packages are installed."""
    required = ['flask', 'flask-cors']
    missing = []
    
    for package in required:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"📦 Installing missing packages: {', '.join(missing)}")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing)
        print("✅ Dependencies installed!")

def check_environment():
    """Check if environment is properly configured."""
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️  Warning: OPENAI_API_KEY not found in environment")
        print("   Make sure your .env file is configured")
    
    chat_dir = Path('ChatExports')
    if not chat_dir.exists():
        print("⚠️  Warning: ChatExports directory not found")
        print("   Make sure your conversation files are in the ChatExports folder")

def main():
    """Start the API server."""
    print("🚀 Starting Coaching Recap API for Custom ChatGPT...")
    print("=" * 60)
    
    # Check dependencies
    check_dependencies()
    
    # Check environment
    check_environment()
    
    print("\n🔗 API will be available at: http://localhost:5000")
    print("📡 Available endpoints:")
    print("  GET  /recap/30days - 30-day analysis")
    print("  GET  /recap/7days - 7-day analysis") 
    print("  POST /recap/custom - Custom timeframe")
    print("  GET  /summary/quick - Quick summary")
    print("  GET  /files/list - List recent files")
    
    print("\n🤖 To create your Custom ChatGPT:")
    print("1. Go to ChatGPT → Create a GPT")
    print("2. Follow the instructions in custom_gpt_config.md")
    print("3. Set base URL to: http://localhost:5000")
    
    print("\n🎯 Starting server...")
    
    # Start the API server
    try:
        from api_server import app
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n⏹️  Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
