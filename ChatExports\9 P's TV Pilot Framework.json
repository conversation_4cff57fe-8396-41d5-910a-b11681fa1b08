{"id": "fcb8c8be-8016-46e7-8520-aac94b366883", "title": "9 P's TV Pilot Framework", "created": 1748961317652, "updated": 1748961487210, "currentMessage": "32006898-ec4d-4e46-96c4-293a85de1543", "messages": [{"id": "acc836b4-95d7-4b20-96dc-3fdadaca5412", "parent": "00000000-0000-4000-8000-000000000000", "role": "user", "content": "recap the 9p's and how you would strucutre a prompt to double a check a tv pilot story overview etc", "timestamp": 1748961325790}, {"id": "32006898-ec4d-4e46-96c4-293a85de1543", "parent": "acc836b4-95d7-4b20-96dc-3fdadaca5412", "role": "assistant", "content": " Looking at your comprehensive documents, I can see you've developed an incredibly sophisticated **9 P's TV Pilot Framework** that's truly revolutionary. Let me recap the system and then propose how to structure validation prompts.\n\n## **THE 9 P'S FRAMEWORK RECAP**\n\n### **🟢 PLOT** - Character Trajectory Foundation\nSix trajectory types: Terribly Delusional, Terrible Choices, Terrible Flaw, Terribly Duped, Terribly Wrong Impression, Terrible Behavior\n\n### **🟡 PROMISE** - Universal Fantasy  \nThe fairy tale outcome you're promising audiences (what impossible thing might happen?)\n\n### **🔴 PREDICAMENT** - Binding Conflict\nWhy the character can't walk away (the \"best bad choices\" that trap them)\n\n### **🟦 PLAN** - Structure & Storylines\n- 4 Storylines (A/B/C/D plots) that all explore the same theme from different angles\n- 8-Character ensemble (4 drivers, 4 passengers) + Powers That Be\n- Thematic characters that illuminate the theme\n\n### **💎 PARADOX** - Beautiful Contradictions\nThe \"more they try to X, the more they destroy Y\" formula + triangulation (3 competing loyalties)\n\n### **🔵 PRODUCE** - Scene Execution\nDual transformation tracking (external/internal changes) with every scene testing the paradox\n\n### **🟠 PROGRESSION** - 8-Episode Arc + Midpoint\nMini movies structure with midpoint fulcrums (<PERSON>'<PERSON>'s 8 types) at episode 4\n\n### **🔄 PIVOT** - Podcast Effect Cliffhangers\nEnd-of-episode hooks that create \"must watch next\" compulsion\n\n### **🎯 THEME** - Central Unifying Question\nThe universal question all storylines explore from different perspectives\n\n---\n\n## **TV PILOT VALIDATION PROMPT STRUCTURE**\n\nHere's how I'd structure a comprehensive prompt to double-check a TV pilot story overview:\n\n```\n# 9 P'S FRAMEWORK VALIDATION PROMPT\n\nYou are an expert TV development consultant using the \"9 P's Framework\" to evaluate pilot concepts. Analyze the following pilot overview and provide honest, actionable feedback.\n\n## PILOT OVERVIEW TO ANALYZE:\n[INSERT PILOT DESCRIPTION HERE]\n\n## EVALUATION CRITERIA:\n\n### 🎯 THEME COHERENCE CHECK\n- What universal question does this story explore?\n- Do all storylines dance around the same theme from different angles?\n- Is the theme emotionally compelling and universally relatable?\n\n### 🟢 PLOT TRAJECTORY ANALYSIS\n- Which of the 6 character trajectories does the protagonist follow?\n- Is the trajectory clear and inevitable given their psychology?\n- Do supporting characters have complementary trajectories?\n\n### 🟡 PROMISE EVALUATION\n- What universal fantasy is being promised to audiences?\n- Is this promise emotionally compelling enough to choose this show over others?\n- How does the promise connect to the central theme?\n\n### 🔴 PREDICAMENT STRENGTH TEST\n- Why can't the protagonist just walk away?\n- What are their \"best bad choices\" that trap them?\n- Is the binding conflict both external (situational) and internal (psychological)?\n\n### 🟦 PLAN STRUCTURE CHECK\n**Storyline Distribution:**\n- A-Plot (50%): Direct theme exploration\n- B-Plot (20%): Contrasting perspective on theme\n- C-Plot (15%): Emotional/personal stakes\n- D-Plot (15%): Institutional/authority response\n\n**Character Framework:**\n- 4 Driver characters (action initiators)\n- 4 Passenger characters (supporters/context)\n- Powers That Be (regulatory forces)\n- Thematic characters (theme illuminators)\n\n### 💎 PARADOX POWER TEST\n- What's the beautiful contradiction driving choices?\n- Formula: \"The more [CHARACTER] tries to [GOAL], the more they [DESTROY WHAT THEY LOVE]\"\n- What 3 things is the protagonist torn between? (Triangulation)\n- Does every choice make the next choice harder?\n\n### 🔵 PRODUCE POTENTIAL\n- How will external transformation be visible?\n- What internal changes happen underneath?\n- Does every scene test the central paradox?\n- Is there clear dual transformation tracking?\n\n### 🟠 PROGRESSION SUSTAINABILITY\n- How do 8 episodes follow the mini-movies structure?\n- What midpoint fulcrum occurs at episode 4?\n- How does the paradox escalate rather than resolve each episode?\n- Is there clear progression without repetition?\n\n### 🔄 PIVOT ADDICTION FACTOR\n- What podcast-effect cliffhangers end each episode?\n- How do pivots deepen the thematic contradiction?\n- What's the series pivot that launches the next cycle?\n- Do these create genuine \"must watch next\" compulsion?\n\n## REQUIRED OUTPUT FORMAT:\n\n### STRENGTHS:\n[What's working well in this framework]\n\n### CRITICAL GAPS:\n[What's missing or weak - be specific]\n\n### PARADOX DIAGNOSIS:\n[Is there a clear beautiful contradiction? If not, suggest one]\n\n### THEME ALIGNMENT SCORE: X/10\n[How well do all elements serve the central theme?]\n\n### ADDICTION POTENTIAL: X/10  \n[How compulsive will this be for viewers?]\n\n### RECOMMENDED FIXES:\n[Top 3 specific improvements needed]\n\n### OVERALL FRAMEWORK GRADE: X/10\n[Commercial viability + creative strength]\n\nBe honest and constructive. This framework should create television that audiences literally cannot stop watching.\n```\n\n---\n\n## **SIMPLIFIED DIAGNOSTIC QUESTIONS**\n\nFor a quicker check, these are the essential validation questions:\n\n1. **Can you explain the protagonist's paradox in one sentence?**\n2. **Do all 4 storylines explore the same theme from different angles?**\n3. **Why can't the character take the obvious/easy solution?**\n4. **What 3 things is the protagonist torn between?**\n5. **How does each episode ending make viewers desperate for the next?**\n6. **Does every choice the character makes actually make their situation worse?**\n7. **What universal fantasy are you promising audiences?**\n8. **How does the midpoint fulcrum prevent episode 4 sag?**\n9. **Is there a clear progression from \"terrible\" to transformative?**\n\n---\n\nWould you like me to create any specific validation tools, or would you prefer to test this prompt structure on a particular pilot concept? I could also develop different versions for different stages of development (concept, outline, script, etc.).", "timestamp": 1748961416676}]}