"""
Export Assistant - Unified export system for local and ChatGPT integration
"""
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
from local_export_manager import LocalExportManager
import config

class ExportAssistant:
    """Intelligent export assistant that helps users choose and create exports."""
    
    def __init__(self):
        """Initialize the export assistant."""
        self.local_exporter = LocalExportManager()
        self.export_recommendations = {
            'sharing_with_coach': ['summary', 'insights'],
            'personal_reflection': ['insights', 'summary'],
            'data_analysis': ['csv', 'json'],
            'progress_tracking': ['summary', 'csv'],
            'detailed_review': ['json', 'insights'],
            'quick_overview': ['summary'],
            'spreadsheet_analysis': ['csv'],
            'development_planning': ['insights', 'summary']
        }
    
    def analyze_export_needs(self, user_intent: str) -> Dict[str, Any]:
        """
        Analyze user intent and recommend export formats.
        
        Args:
            user_intent: Description of what the user wants to do
            
        Returns:
            Dictionary with recommendations and explanations
        """
        intent_lower = user_intent.lower()
        
        # Determine use case
        use_case = 'general'
        for case, keywords in {
            'sharing_with_coach': ['coach', 'share', 'send', 'show'],
            'personal_reflection': ['reflect', 'review', 'understand', 'insight'],
            'data_analysis': ['analyze', 'data', 'excel', 'chart', 'graph'],
            'progress_tracking': ['progress', 'track', 'monitor', 'trend'],
            'detailed_review': ['detailed', 'complete', 'full', 'everything'],
            'quick_overview': ['quick', 'summary', 'overview', 'brief'],
            'spreadsheet_analysis': ['spreadsheet', 'excel', 'csv', 'filter'],
            'development_planning': ['plan', 'future', 'next', 'develop', 'grow']
        }.items():
            if any(keyword in intent_lower for keyword in keywords):
                use_case = case
                break
        
        # Get recommendations
        recommended_formats = self.export_recommendations.get(use_case, ['summary', 'insights'])
        
        # Load current results to provide context
        try:
            data = self.local_exporter.load_results()
            summary = data.get('summary', {})
            
            context = {
                'total_conversations': summary.get('total_conversations', 0),
                'period': summary.get('period', 'Unknown'),
                'top_arc_type': self._get_top_arc_type(summary),
                'high_quality_count': len(summary.get('high_quality_sessions', [])),
                'success_rate': summary.get('processing_stats', {}).get('success_rate', '0%')
            }
        except:
            context = {'error': 'No analysis results found'}
        
        return {
            'use_case': use_case,
            'recommended_formats': recommended_formats,
            'context': context,
            'explanations': self._get_format_explanations(),
            'recommendations': self._get_use_case_explanation(use_case)
        }
    
    def create_custom_export(self, formats: List[str], user_note: Optional[str] = None) -> Dict[str, str]:
        """
        Create exports in specified formats with optional user note.
        
        Args:
            formats: List of format names to export
            user_note: Optional note to include in exports
            
        Returns:
            Dictionary mapping format names to file paths
        """
        results = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for format_name in formats:
            try:
                if format_name == 'json':
                    file_path = self.local_exporter.export_json()
                elif format_name == 'csv':
                    file_path = self.local_exporter.export_csv()
                elif format_name == 'summary':
                    file_path = self.local_exporter.export_summary()
                elif format_name == 'insights':
                    file_path = self.local_exporter.export_insights()
                else:
                    results[format_name] = f"Error: Unknown format '{format_name}'"
                    continue
                
                # Add user note if provided
                if user_note and format_name in ['summary', 'insights']:
                    self._add_user_note_to_file(file_path, user_note)
                
                results[format_name] = file_path
                
            except Exception as e:
                results[format_name] = f"Error: {e}"
        
        return results
    
    def get_export_status(self) -> Dict[str, Any]:
        """Get current export status and available data."""
        try:
            data = self.local_exporter.load_results()
            summary = data.get('summary', {})
            metadata = data.get('metadata', {})
            
            # Check for existing exports
            export_dir = Path("exports")
            existing_exports = []
            if export_dir.exists():
                for file_path in export_dir.glob("coaching_*"):
                    existing_exports.append({
                        'name': file_path.name,
                        'size': file_path.stat().st_size,
                        'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
            
            return {
                'analysis_available': True,
                'total_conversations': summary.get('total_conversations', 0),
                'analysis_date': metadata.get('processed_at', 'Unknown'),
                'period': summary.get('period', 'Unknown'),
                'success_rate': summary.get('processing_stats', {}).get('success_rate', '0%'),
                'existing_exports': existing_exports,
                'export_formats_available': ['json', 'csv', 'summary', 'insights']
            }
            
        except FileNotFoundError:
            return {
                'analysis_available': False,
                'error': 'No analysis results found. Run analysis first.',
                'existing_exports': [],
                'export_formats_available': []
            }
    
    def generate_chatgpt_response(self, user_query: str) -> str:
        """
        Generate a response for Custom ChatGPT integration.
        
        Args:
            user_query: User's query about exports
            
        Returns:
            Formatted response for ChatGPT
        """
        # Analyze the query
        analysis = self.analyze_export_needs(user_query)
        status = self.get_export_status()
        
        if not status['analysis_available']:
            return """
🚫 **No Analysis Data Available**

I don't see any coaching analysis results to export. You'll need to run the analysis first:

```bash
python optimized_coaching_agent.py
```

Once you have analysis results, I can help you export them in multiple formats!
"""
        
        # Build response
        response_parts = [
            f"🎯 **Export Recommendations for Your Coaching Analysis**",
            f"",
            f"📊 **Your Data Overview:**",
            f"- **{status['total_conversations']} conversations** analyzed",
            f"- **Period:** {status['period']}",
            f"- **Success Rate:** {status['success_rate']}",
            f"- **Analysis Date:** {status['analysis_date'][:10]}",
            f""
        ]
        
        # Add context-specific recommendations
        context = analysis['context']
        if 'top_arc_type' in context:
            response_parts.extend([
                f"🎭 **Your Dominant Pattern:** {context['top_arc_type']}",
                f"⭐ **High-Quality Sessions:** {context.get('high_quality_count', 0)}",
                f""
            ])
        
        # Add format recommendations
        use_case = analysis['use_case']
        recommended = analysis['recommended_formats']
        
        response_parts.extend([
            f"💡 **Recommended Formats for '{use_case.replace('_', ' ').title()}':**",
            f""
        ])
        
        for format_name in recommended:
            explanation = analysis['explanations'][format_name]
            response_parts.append(f"- **{format_name.upper()}**: {explanation}")
        
        response_parts.extend([
            f"",
            f"🚀 **How to Export:**",
            f"",
            f"**Option 1: Local Command Line**",
            f"```bash",
            f"python local_export_manager.py {' '.join(recommended)}",
            f"```",
            f"",
            f"**Option 2: Web Interface**",
            f"Visit: http://localhost:5000",
            f"",
            f"**Option 3: Export All Formats**",
            f"```bash",
            f"python local_export_manager.py all",
            f"```"
        ])
        
        # Add existing exports info
        if status['existing_exports']:
            response_parts.extend([
                f"",
                f"📁 **Recent Exports:**"
            ])
            for export in status['existing_exports'][-3:]:  # Show last 3
                size_kb = export['size'] // 1024
                response_parts.append(f"- {export['name']} ({size_kb} KB)")
        
        response_parts.extend([
            f"",
            f"🔒 **All exports are created locally** - your data stays private!",
            f"",
            f"Need help choosing the right format? Just ask! 🤖"
        ])
        
        return '\n'.join(response_parts)
    
    def _get_top_arc_type(self, summary: Dict[str, Any]) -> str:
        """Get the most common arc type."""
        arc_dist = summary.get('arc_type_distribution', {})
        if not arc_dist:
            return 'Unknown'
        
        top_arc = max(arc_dist.items(), key=lambda x: x[1])[0]
        return top_arc.replace('_', ' ').title()
    
    def _get_format_explanations(self) -> Dict[str, str]:
        """Get explanations for each export format."""
        return {
            'json': 'Complete data with all details - perfect for developers or detailed analysis',
            'csv': 'Spreadsheet format for Excel analysis, charts, and filtering',
            'summary': 'Clean text report with key insights - great for sharing',
            'insights': 'Markdown with deep patterns and growth recommendations'
        }
    
    def _get_use_case_explanation(self, use_case: str) -> str:
        """Get explanation for the identified use case."""
        explanations = {
            'sharing_with_coach': 'For sharing with your coach, I recommend the Summary (easy to read) and Insights (actionable recommendations).',
            'personal_reflection': 'For personal reflection, the Insights format provides deep patterns and the Summary gives a clear overview.',
            'data_analysis': 'For data analysis, use CSV for spreadsheets and JSON for complete programmatic access.',
            'progress_tracking': 'For tracking progress, the Summary shows key metrics and CSV allows trend analysis.',
            'detailed_review': 'For detailed review, JSON has everything and Insights provides meaningful patterns.',
            'quick_overview': 'For a quick overview, the Summary format is perfect.',
            'spreadsheet_analysis': 'For spreadsheet work, CSV format is ideal for Excel analysis.',
            'development_planning': 'For planning your development, Insights provides recommendations and Summary shows current state.'
        }
        return explanations.get(use_case, 'Choose the format that best matches your needs.')
    
    def _add_user_note_to_file(self, file_path: str, note: str):
        """Add a user note to a text-based export file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add note at the top
            note_section = f"""
USER NOTE:
{note}

{'=' * 50}

"""
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(note_section + content)
                
        except Exception:
            pass  # Silently fail if we can't add the note

def main():
    """Main function for testing the export assistant."""
    assistant = ExportAssistant()
    
    # Test different queries
    test_queries = [
        "I want to share my progress with my coach",
        "Help me analyze my data in Excel",
        "I need a quick overview of my coaching sessions",
        "I want to reflect on my personal growth patterns"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: {query}")
        print("-" * 50)
        response = assistant.generate_chatgpt_response(query)
        print(response)
        print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
